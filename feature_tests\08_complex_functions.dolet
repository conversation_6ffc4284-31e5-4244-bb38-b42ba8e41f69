# Complex Functions Test
# Testing more complex function implementations

# Power function with recursion
fun power(base: float, exponent: int) -> float:
    if exponent == 0:
        return 1.0
    else:
        if exponent > 0:
            return base * power(base, exponent - 1)
        else:
            return 1.0 / power(base, -exponent)
        end
    end
end

# Function that calls other functions
fun calculate_area_and_perimeter(radius: float) -> float:
    set area = 3.14159 * power(radius, 2)
    set perimeter = 2.0 * 3.14159 * radius
    return area + perimeter
end

# Function with complex logic
fun classify_number(n: int) -> string:
    if n > 0:
        if n % 2 == 0:
            return "positive even"
        else:
            return "positive odd"
        end
    else:
        if n == 0:
            return "zero"
        else:
            return "negative"
        end
    end
end

# Test complex functions
set power_2_5 = power(2.0, 5)
set power_3_neg2 = power(3.0, -2)
set circle_calc = calculate_area_and_perimeter(5.0)
set classify_10 = classify_number(10)
set classify_neg7 = classify_number(-7)
set classify_0 = classify_number(0)

# Output results
say "========================================="
say "Complex Functions Test"
say "========================================="

say "Power Function Results:"
say "power(2.0, 5) = " + power_2_5
say "power(3.0, -2) = " + power_3_neg2

say ""
say "Complex Calculation:"
say "area + perimeter for radius 5.0 = " + circle_calc

say ""
say "Number Classification:"
say "classify_number(10) = " + classify_10
say "classify_number(-7) = " + classify_neg7
say "classify_number(0) = " + classify_0

say "========================================="

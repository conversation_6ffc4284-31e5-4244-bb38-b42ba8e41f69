{"$message_type":"diagnostic","message":"unused imports: `Write` and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":14,"byte_end":18,"line_start":1,"line_end":1,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":20,"byte_end":25,"line_start":1,"line_end":1,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":0,"byte_end":28,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::io::{self, Write};","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Write` and `self`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:1:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{self, Write};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":66,"byte_end":67,"line_start":4,"line_end":4,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":73,"byte_end":74,"line_start":4,"line_end":4,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_parens)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":66,"byte_end":67,"line_start":4,"line_end":4,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":73,"byte_end":74,"line_start":4,"line_end":4,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:4:8\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (n <= 1) {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_parens)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn <= 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if n <= 1 {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `return` value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":123,"byte_end":124,"line_start":7,"line_end":7,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":146,"byte_end":147,"line_start":7,"line_end":7,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":123,"byte_end":124,"line_start":7,"line_end":7,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":146,"byte_end":147,"line_start":7,"line_end":7,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `return` value\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:7:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return (n * factorial((n - 1)));\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn * factorial((n - 1))\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return n * factorial((n - 1));\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":138,"byte_end":139,"line_start":7,"line_end":7,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":144,"byte_end":145,"line_start":7,"line_end":7,"column_start":37,"column_end":38,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":37,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":138,"byte_end":139,"line_start":7,"line_end":7,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":144,"byte_end":145,"line_start":7,"line_end":7,"column_start":37,"column_end":38,"is_primary":true,"text":[{"text":"        return (n * factorial((n - 1)));","highlight_start":37,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:7:31\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return (n * factorial((n - 1)));\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return (n * factorial(\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn - 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return (n * factorial(n - 1));\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":201,"byte_end":202,"line_start":13,"line_end":13,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":208,"byte_end":209,"line_start":13,"line_end":13,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":201,"byte_end":202,"line_start":13,"line_end":13,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":208,"byte_end":209,"line_start":13,"line_end":13,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (n <= 1) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:13:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (n <= 1) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn <= 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if n <= 1 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `return` value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":258,"byte_end":259,"line_start":16,"line_end":16,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":298,"byte_end":299,"line_start":16,"line_end":16,"column_start":56,"column_end":57,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":56,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":258,"byte_end":259,"line_start":16,"line_end":16,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":298,"byte_end":299,"line_start":16,"line_end":16,"column_start":56,"column_end":57,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":56,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `return` value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:16:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return (fibonacci((n - 1)) + fibonacci((n - 2)));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfibonacci((n - 1)) + fibonacci((n - 2))\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return fibonacci((n - 1)) + fibonacci((n - 2));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":269,"byte_end":270,"line_start":16,"line_end":16,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":275,"byte_end":276,"line_start":16,"line_end":16,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":269,"byte_end":270,"line_start":16,"line_end":16,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":275,"byte_end":276,"line_start":16,"line_end":16,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:16:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return (fibonacci((n - 1)) + fibonacci((n - 2)));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return (fibonacci(\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn - 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m) + fibonacci((n - 2)));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return (fibonacci(n - 1) + fibonacci((n - 2)));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":290,"byte_end":291,"line_start":16,"line_end":16,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":296,"byte_end":297,"line_start":16,"line_end":16,"column_start":54,"column_end":55,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":54,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":290,"byte_end":291,"line_start":16,"line_end":16,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":296,"byte_end":297,"line_start":16,"line_end":16,"column_start":54,"column_end":55,"is_primary":true,"text":[{"text":"        return (fibonacci((n - 1)) + fibonacci((n - 2)));","highlight_start":54,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:16:48\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return (fibonacci((n - 1)) + fibonacci((n - 2)));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return (fibonacci((n - 1)) + fibonacci(\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mn - 2\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return (fibonacci((n - 1)) + fibonacci(n - 2));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":367,"byte_end":368,"line_start":22,"line_end":22,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (exponent == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":381,"byte_end":382,"line_start":22,"line_end":22,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"    if (exponent == 0) {","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":367,"byte_end":368,"line_start":22,"line_end":22,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (exponent == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":381,"byte_end":382,"line_start":22,"line_end":22,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"    if (exponent == 0) {","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:22:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (exponent == 0) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mexponent == 0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if exponent == 0 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":430,"byte_end":431,"line_start":25,"line_end":25,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"        if (exponent > 0) {","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":443,"byte_end":444,"line_start":25,"line_end":25,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"        if (exponent > 0) {","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":430,"byte_end":431,"line_start":25,"line_end":25,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"        if (exponent > 0) {","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":443,"byte_end":444,"line_start":25,"line_end":25,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"        if (exponent > 0) {","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:25:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if (exponent > 0) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mexponent > 0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if exponent > 0 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `return` value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":466,"byte_end":467,"line_start":26,"line_end":26,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":515,"byte_end":516,"line_start":26,"line_end":26,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":466,"byte_end":467,"line_start":26,"line_end":26,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":515,"byte_end":516,"line_start":26,"line_end":26,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `return` value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:26:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return (base as f32 * power(base, (exponent - 1)) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mbase as f32 * power(base, (exponent - 1)) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return base as f32 * power(base, (exponent - 1)) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":493,"byte_end":494,"line_start":26,"line_end":26,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":506,"byte_end":507,"line_start":26,"line_end":26,"column_start":60,"column_end":61,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":60,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":493,"byte_end":494,"line_start":26,"line_end":26,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":506,"byte_end":507,"line_start":26,"line_end":26,"column_start":60,"column_end":61,"is_primary":true,"text":[{"text":"            return (base as f32 * power(base, (exponent - 1)) as f32);","highlight_start":60,"highlight_end":61}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:26:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return (base as f32 * power(base, (exponent - 1)) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return (base as f32 * power(base, \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mexponent - 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m) as f32);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return (base as f32 * power(base, exponent - 1) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `return` value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":554,"byte_end":555,"line_start":28,"line_end":28,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":600,"byte_end":601,"line_start":28,"line_end":28,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":554,"byte_end":555,"line_start":28,"line_end":28,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":600,"byte_end":601,"line_start":28,"line_end":28,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `return` value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:28:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return (1f32 as f32 / power(base, (-exponent)) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m1f32 as f32 / power(base, (-exponent)) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return 1f32 as f32 / power(base, (-exponent)) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":581,"byte_end":582,"line_start":28,"line_end":28,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":591,"byte_end":592,"line_start":28,"line_end":28,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":581,"byte_end":582,"line_start":28,"line_end":28,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":591,"byte_end":592,"line_start":28,"line_end":28,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"            return (1f32 as f32 / power(base, (-exponent)) as f32);","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:28:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return (1f32 as f32 / power(base, (-exponent)) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            return (1f32 as f32 / power(base, \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m-exponent\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m) as f32);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            return (1f32 as f32 / power(base, -exponent) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":669,"byte_end":670,"line_start":35,"line_end":35,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (b == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":676,"byte_end":677,"line_start":35,"line_end":35,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (b == 0) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":669,"byte_end":670,"line_start":35,"line_end":35,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (b == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":676,"byte_end":677,"line_start":35,"line_end":35,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    if (b == 0) {","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:35:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (b == 0) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mb == 0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if b == 0 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":733,"byte_end":734,"line_start":38,"line_end":38,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"        return gcd(b, (a % b));","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":739,"byte_end":740,"line_start":38,"line_end":38,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"        return gcd(b, (a % b));","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":733,"byte_end":734,"line_start":38,"line_end":38,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"        return gcd(b, (a % b));","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":739,"byte_end":740,"line_start":38,"line_end":38,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"        return gcd(b, (a % b));","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:38:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return gcd(b, (a % b));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        return gcd(b, \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0ma % b\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        return gcd(b, a % b);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":1028,"byte_end":1029,"line_start":51,"line_end":51,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    let mut negative_int = (-123);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":1033,"byte_end":1034,"line_start":51,"line_end":51,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut negative_int = (-123);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":1028,"byte_end":1029,"line_start":51,"line_end":51,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    let mut negative_int = (-123);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":1033,"byte_end":1034,"line_start":51,"line_end":51,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut negative_int = (-123);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:51:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut negative_int = (-123);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut negative_int = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m-123\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut negative_int = -123;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2249,"byte_end":2250,"line_start":76,"line_end":76,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"    let mut circle_area = (PI as f32 * power(5f32, 2) as f32);","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2283,"byte_end":2284,"line_start":76,"line_end":76,"column_start":61,"column_end":62,"is_primary":true,"text":[{"text":"    let mut circle_area = (PI as f32 * power(5f32, 2) as f32);","highlight_start":61,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2249,"byte_end":2250,"line_start":76,"line_end":76,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"    let mut circle_area = (PI as f32 * power(5f32, 2) as f32);","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2283,"byte_end":2284,"line_start":76,"line_end":76,"column_start":61,"column_end":62,"is_primary":true,"text":[{"text":"    let mut circle_area = (PI as f32 * power(5f32, 2) as f32);","highlight_start":61,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:76:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut circle_area = (PI as f32 * power(5f32, 2) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut circle_area = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mPI as f32 * power(5f32, 2) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut circle_area = PI as f32 * power(5f32, 2) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2314,"byte_end":2315,"line_start":77,"line_end":77,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut sphere_volume = (((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2380,"byte_end":2381,"line_start":77,"line_end":77,"column_start":95,"column_end":96,"is_primary":true,"text":[{"text":"    let mut sphere_volume = (((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32);","highlight_start":95,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2314,"byte_end":2315,"line_start":77,"line_end":77,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut sphere_volume = (((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2380,"byte_end":2381,"line_start":77,"line_end":77,"column_start":95,"column_end":96,"is_primary":true,"text":[{"text":"    let mut sphere_volume = (((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32);","highlight_start":95,"highlight_end":96}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:77:29\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut sphere_volume = (((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut sphere_volume = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut sphere_volume = ((4f32 / 3f32) as f32 * PI as f32) as f32 * power(3f32, 3) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2415,"byte_end":2416,"line_start":78,"line_end":78,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut compound_interest = (1000f32 as f32 * power(1.05f32, 10) as f32);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2458,"byte_end":2459,"line_start":78,"line_end":78,"column_start":76,"column_end":77,"is_primary":true,"text":[{"text":"    let mut compound_interest = (1000f32 as f32 * power(1.05f32, 10) as f32);","highlight_start":76,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2415,"byte_end":2416,"line_start":78,"line_end":78,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut compound_interest = (1000f32 as f32 * power(1.05f32, 10) as f32);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2458,"byte_end":2459,"line_start":78,"line_end":78,"column_start":76,"column_end":77,"is_primary":true,"text":[{"text":"    let mut compound_interest = (1000f32 as f32 * power(1.05f32, 10) as f32);","highlight_start":76,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:78:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut compound_interest = (1000f32 as f32 * power(1.05f32, 10) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut compound_interest = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m1000f32 as f32 * power(1.05f32, 10) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut compound_interest = 1000f32 as f32 * power(1.05f32, 10) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2708,"byte_end":2709,"line_start":85,"line_end":85,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2798,"byte_end":2799,"line_start":85,"line_end":85,"column_start":118,"column_end":119,"is_primary":true,"text":[{"text":"    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);","highlight_start":118,"highlight_end":119}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2708,"byte_end":2709,"line_start":85,"line_end":85,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2798,"byte_end":2799,"line_start":85,"line_end":85,"column_start":118,"column_end":119,"is_primary":true,"text":[{"text":"    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);","highlight_start":118,"highlight_end":119}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:85:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut complex_calc = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut complex_calc = ((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2826,"byte_end":2827,"line_start":86,"line_end":86,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"    let mut ratio_test = ((GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32));","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2887,"byte_end":2888,"line_start":86,"line_end":86,"column_start":87,"column_end":88,"is_primary":true,"text":[{"text":"    let mut ratio_test = ((GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32));","highlight_start":87,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2826,"byte_end":2827,"line_start":86,"line_end":86,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"    let mut ratio_test = ((GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32));","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2887,"byte_end":2888,"line_start":86,"line_end":86,"column_start":87,"column_end":88,"is_primary":true,"text":[{"text":"    let mut ratio_test = ((GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32));","highlight_start":87,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:86:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut ratio_test = ((GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut ratio_test = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m(GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32)\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut ratio_test = (GOLDEN_RATIO as f32 * PI as f32) / (E as f32 + 1f32 as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2897,"byte_end":2898,"line_start":87,"line_end":87,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (version >= 1f32) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":2913,"byte_end":2914,"line_start":87,"line_end":87,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"    if (version >= 1f32) {","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2897,"byte_end":2898,"line_start":87,"line_end":87,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (version >= 1f32) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":2913,"byte_end":2914,"line_start":87,"line_end":87,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"    if (version >= 1f32) {","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:87:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (version >= 1f32) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mversion >= 1f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if version >= 1f32 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":2988,"byte_end":2989,"line_start":89,"line_end":89,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3005,"byte_end":3006,"line_start":89,"line_end":89,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2988,"byte_end":2989,"line_start":89,"line_end":89,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3005,"byte_end":3006,"line_start":89,"line_end":89,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:89:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        passed_tests = (passed_tests + 1);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        passed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mpassed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        passed_tests = passed_tests + 1;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3092,"byte_end":3093,"line_start":92,"line_end":92,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3109,"byte_end":3110,"line_start":92,"line_end":92,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3092,"byte_end":3093,"line_start":92,"line_end":92,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3109,"byte_end":3110,"line_start":92,"line_end":92,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:92:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        failed_tests = (failed_tests + 1);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        failed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        failed_tests = failed_tests + 1;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3125,"byte_end":3126,"line_start":94,"line_end":94,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (fact_5 == 120) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3139,"byte_end":3140,"line_start":94,"line_end":94,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"    if (fact_5 == 120) {","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3125,"byte_end":3126,"line_start":94,"line_end":94,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (fact_5 == 120) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3139,"byte_end":3140,"line_start":94,"line_end":94,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"    if (fact_5 == 120) {","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:94:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (fact_5 == 120) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfact_5 == 120\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if fact_5 == 120 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3248,"byte_end":3249,"line_start":96,"line_end":96,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3265,"byte_end":3266,"line_start":96,"line_end":96,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3248,"byte_end":3249,"line_start":96,"line_end":96,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3265,"byte_end":3266,"line_start":96,"line_end":96,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:96:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        passed_tests = (passed_tests + 1);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        passed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mpassed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        passed_tests = passed_tests + 1;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3386,"byte_end":3387,"line_start":99,"line_end":99,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3403,"byte_end":3404,"line_start":99,"line_end":99,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3386,"byte_end":3387,"line_start":99,"line_end":99,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3403,"byte_end":3404,"line_start":99,"line_end":99,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:99:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        failed_tests = (failed_tests + 1);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        failed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        failed_tests = failed_tests + 1;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3419,"byte_end":3420,"line_start":101,"line_end":101,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (circle_area > 75f32) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3439,"byte_end":3440,"line_start":101,"line_end":101,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    if (circle_area > 75f32) {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3419,"byte_end":3420,"line_start":101,"line_end":101,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (circle_area > 75f32) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3439,"byte_end":3440,"line_start":101,"line_end":101,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    if (circle_area > 75f32) {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:101:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (circle_area > 75f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mcircle_area > 75f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if circle_area > 75f32 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3557,"byte_end":3558,"line_start":103,"line_end":103,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3574,"byte_end":3575,"line_start":103,"line_end":103,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3557,"byte_end":3558,"line_start":103,"line_end":103,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3574,"byte_end":3575,"line_start":103,"line_end":103,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        passed_tests = (passed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:103:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        passed_tests = (passed_tests + 1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        passed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mpassed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        passed_tests = passed_tests + 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3704,"byte_end":3705,"line_start":106,"line_end":106,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3721,"byte_end":3722,"line_start":106,"line_end":106,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3704,"byte_end":3705,"line_start":106,"line_end":106,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3721,"byte_end":3722,"line_start":106,"line_end":106,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:106:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        failed_tests = (failed_tests + 1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        failed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        failed_tests = failed_tests + 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3737,"byte_end":3738,"line_start":108,"line_end":108,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (is_testing == true) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3756,"byte_end":3757,"line_start":108,"line_end":108,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"    if (is_testing == true) {","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3737,"byte_end":3738,"line_start":108,"line_end":108,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (is_testing == true) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3756,"byte_end":3757,"line_start":108,"line_end":108,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"    if (is_testing == true) {","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:108:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (is_testing == true) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mis_testing == true\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if is_testing == true {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3771,"byte_end":3772,"line_start":109,"line_end":109,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"        if (debug_mode == true) {","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3790,"byte_end":3791,"line_start":109,"line_end":109,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"        if (debug_mode == true) {","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3771,"byte_end":3772,"line_start":109,"line_end":109,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"        if (debug_mode == true) {","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3790,"byte_end":3791,"line_start":109,"line_end":109,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"        if (debug_mode == true) {","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:109:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if (debug_mode == true) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mdebug_mode == true\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if debug_mode == true {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3873,"byte_end":3874,"line_start":111,"line_end":111,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            passed_tests = (passed_tests + 1);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":3890,"byte_end":3891,"line_start":111,"line_end":111,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"            passed_tests = (passed_tests + 1);","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3873,"byte_end":3874,"line_start":111,"line_end":111,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            passed_tests = (passed_tests + 1);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":3890,"byte_end":3891,"line_start":111,"line_end":111,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"            passed_tests = (passed_tests + 1);","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:111:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            passed_tests = (passed_tests + 1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            passed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mpassed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            passed_tests = passed_tests + 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3991,"byte_end":3992,"line_start":114,"line_end":114,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            failed_tests = (failed_tests + 1);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4008,"byte_end":4009,"line_start":114,"line_end":114,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"            failed_tests = (failed_tests + 1);","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3991,"byte_end":3992,"line_start":114,"line_end":114,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            failed_tests = (failed_tests + 1);","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4008,"byte_end":4009,"line_start":114,"line_end":114,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"            failed_tests = (failed_tests + 1);","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:114:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            failed_tests = (failed_tests + 1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            failed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            failed_tests = failed_tests + 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4109,"byte_end":4110,"line_start":118,"line_end":118,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4126,"byte_end":4127,"line_start":118,"line_end":118,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4109,"byte_end":4110,"line_start":118,"line_end":118,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4126,"byte_end":4127,"line_start":118,"line_end":118,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"        failed_tests = (failed_tests + 1);","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:118:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        failed_tests = (failed_tests + 1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        failed_tests = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests + 1\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        failed_tests = failed_tests + 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4287,"byte_end":4288,"line_start":122,"line_end":122,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut expression_result = (power(PI as f32, 2) as f32 + power(E as f32, 2) as f32);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4342,"byte_end":4343,"line_start":122,"line_end":122,"column_start":88,"column_end":89,"is_primary":true,"text":[{"text":"    let mut expression_result = (power(PI as f32, 2) as f32 + power(E as f32, 2) as f32);","highlight_start":88,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4287,"byte_end":4288,"line_start":122,"line_end":122,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    let mut expression_result = (power(PI as f32, 2) as f32 + power(E as f32, 2) as f32);","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4342,"byte_end":4343,"line_start":122,"line_end":122,"column_start":88,"column_end":89,"is_primary":true,"text":[{"text":"    let mut expression_result = (power(PI as f32, 2) as f32 + power(E as f32, 2) as f32);","highlight_start":88,"highlight_end":89}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:122:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut expression_result = (power(PI as f32, 2) as f32 + power(E as f32, 2) as f32);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut expression_result = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mpower(PI as f32, 2) as f32 + power(E as f32, 2) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut expression_result = power(PI as f32, 2) as f32 + power(E as f32, 2) as f32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4375,"byte_end":4376,"line_start":123,"line_end":123,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"    let mut combined_result = (factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32);","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4426,"byte_end":4427,"line_start":123,"line_end":123,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"    let mut combined_result = (factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32);","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4375,"byte_end":4376,"line_start":123,"line_end":123,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"    let mut combined_result = (factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32);","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4426,"byte_end":4427,"line_start":123,"line_end":123,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"    let mut combined_result = (factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32);","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:123:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut combined_result = (factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut combined_result = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfactorial(gcd(12, 8)) as f32 + fibonacci(7) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut combined_result = factorial(gcd(12, 8)) as f32 + fibonacci(7) as f32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4590,"byte_end":4591,"line_start":127,"line_end":127,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_1 = ((power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4670,"byte_end":4671,"line_start":127,"line_end":127,"column_start":109,"column_end":110,"is_primary":true,"text":[{"text":"    let mut stress_test_1 = ((power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32);","highlight_start":109,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4590,"byte_end":4591,"line_start":127,"line_end":127,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_1 = ((power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4670,"byte_end":4671,"line_start":127,"line_end":127,"column_start":109,"column_end":110,"is_primary":true,"text":[{"text":"    let mut stress_test_1 = ((power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32);","highlight_start":109,"highlight_end":110}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:127:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut stress_test_1 = ((power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut stress_test_1 = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m(power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut stress_test_1 = (power(PI as f32, 3) as f32 * factorial(7) as f32) as f32 / GOLDEN_RATIO as f32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4701,"byte_end":4702,"line_start":128,"line_end":128,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_2 = ((fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4781,"byte_end":4782,"line_start":128,"line_end":128,"column_start":109,"column_end":110,"is_primary":true,"text":[{"text":"    let mut stress_test_2 = ((fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32);","highlight_start":109,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4701,"byte_end":4702,"line_start":128,"line_end":128,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_2 = ((fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4781,"byte_end":4782,"line_start":128,"line_end":128,"column_start":109,"column_end":110,"is_primary":true,"text":[{"text":"    let mut stress_test_2 = ((fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32);","highlight_start":109,"highlight_end":110}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:128:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut stress_test_2 = ((fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut stress_test_2 = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m(fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut stress_test_2 = (fibonacci(16) as f32 + power(E as f32, 3) as f32) as f32 - factorial(6) as f32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around assigned value","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":4812,"byte_end":4813,"line_start":129,"line_end":129,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_3 = (gcd(factorial(8), fibonacci(14)) as f32 * PI as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":4864,"byte_end":4865,"line_start":129,"line_end":129,"column_start":81,"column_end":82,"is_primary":true,"text":[{"text":"    let mut stress_test_3 = (gcd(factorial(8), fibonacci(14)) as f32 * PI as f32);","highlight_start":81,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":4812,"byte_end":4813,"line_start":129,"line_end":129,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    let mut stress_test_3 = (gcd(factorial(8), fibonacci(14)) as f32 * PI as f32);","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":4864,"byte_end":4865,"line_start":129,"line_end":129,"column_start":81,"column_end":82,"is_primary":true,"text":[{"text":"    let mut stress_test_3 = (gcd(factorial(8), fibonacci(14)) as f32 * PI as f32);","highlight_start":81,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around assigned value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:129:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut stress_test_3 = (gcd(factorial(8), fibonacci(14)) as f32 * PI as f32);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut stress_test_3 = \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mgcd(factorial(8), fibonacci(14)) as f32 * PI as f32\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut stress_test_3 = gcd(factorial(8), fibonacci(14)) as f32 * PI as f32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around `if` condition","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":7240,"byte_end":7241,"line_start":172,"line_end":172,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (failed_tests == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":7258,"byte_end":7259,"line_start":172,"line_end":172,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"    if (failed_tests == 0) {","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":7240,"byte_end":7241,"line_start":172,"line_end":172,"column_start":8,"column_end":9,"is_primary":true,"text":[{"text":"    if (failed_tests == 0) {","highlight_start":8,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":7258,"byte_end":7259,"line_start":172,"line_end":172,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"    if (failed_tests == 0) {","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary parentheses around `if` condition\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:172:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if (failed_tests == 0) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    if \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mfailed_tests == 0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    if failed_tests == 0 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":159,"byte_end":160,"line_start":9,"line_end":9,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    0","highlight_start":5,"highlight_end":6}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":85,"byte_end":93,"line_start":5,"line_end":5,"column_start":9,"column_end":17,"is_primary":false,"text":[{"text":"        return 1;","highlight_start":9,"highlight_end":17}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unreachable_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return 1;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    0\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unreachable_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":311,"byte_end":312,"line_start":18,"line_end":18,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    0","highlight_start":5,"highlight_end":6}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":220,"byte_end":228,"line_start":14,"line_end":14,"column_start":9,"column_end":17,"is_primary":false,"text":[{"text":"        return n;","highlight_start":9,"highlight_end":17}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return n;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    0\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":623,"byte_end":626,"line_start":31,"line_end":31,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    0.0","highlight_start":5,"highlight_end":8}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":393,"byte_end":404,"line_start":23,"line_end":23,"column_start":9,"column_end":20,"is_primary":false,"text":[{"text":"        return 1f32;","highlight_start":9,"highlight_end":20}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:31:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return 1f32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    0.0\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":753,"byte_end":754,"line_start":40,"line_end":40,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    0","highlight_start":5,"highlight_end":6}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":688,"byte_end":696,"line_start":36,"line_end":36,"column_start":9,"column_end":17,"is_primary":false,"text":[{"text":"        return a;","highlight_start":9,"highlight_end":17}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:40:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        return a;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    0\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot multiply `i64` by `f32`","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":2728,"byte_end":2729,"line_start":85,"line_end":85,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);","highlight_start":48,"highlight_end":49}],"label":"no implementation for `i64 * f32`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `Mul<f32>` is not implemented for `i64`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Mul<Rhs>`:\n  `&i64` implements `Mul<i64>`\n  `&i64` implements `Mul`\n  `i64` implements `Mul<&i64>`\n  `i64` implements `Mul`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot multiply `i64` by `f32`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:85:48\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut complex_calc = (((fact_5 + fib_10) * (circle_area as f32 / PI as f32)) as f32 + power(E as f32, 2) as f32);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno implementation for `i64 * f32`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the trait `Mul<f32>` is not implemented for `i64`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `Mul<Rhs>`:\u001b[0m\n\u001b[0m             `&i64` implements `Mul<i64>`\u001b[0m\n\u001b[0m             `&i64` implements `Mul`\u001b[0m\n\u001b[0m             `i64` implements `Mul<&i64>`\u001b[0m\n\u001b[0m             `i64` implements `Mul`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 46 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 46 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0277`.\u001b[0m\n"}

# 📌 Note: Token-based Type Inference in Dolet (Enhanced Algorithm)

عند عدم تحديد نوع المتغير يدويًا باستخدام الصيغة:
```dolet
set x: int = 5
```
تقوم لغة Dolet باستخدام أسلوب **"Token-based Type Inference"** لتحديد نوع المتغير تلقائيًا أثناء الترجمة (compile-time)، دون الحاجة لتقييم التعبير أو تحليله بشكل كامل كما هو الحال في بعض اللغات الديناميكية.

---

## 🧠 الفكرة العامة:

يعتمد التحليل على فحص أول رمز (Token) بعد `=` وتطبيق خوارزمية بسيطة وسريعة يمكن تنفيذها مباشرة داخل الـ lexer أو الـ parser.

---

## 🔄 خطوات الخوارزمية التفصيلية:

1. **إذا بدأ التوكن بـ `"`** → يتم اعتباره `String` مباشرة.
2. **إذا بدأ التوكن بـ `'`** → يتم اعتباره `Char`.
3. **إذا كانت القيمة تساوي `true` أو `false` تمامًا** → يتم اعتبارها `Boolean` (يجب أن تكون كلمات محجوزة).
4. **إذا بدأ التوكن برقم (0–9):**
   - يتم التحقق إذا كانت تحتوي على `.`:
     - **إذا لا تحتوي على نقطة** → تعتبر `Int`.
     - **إذا تحتوي على نقطة**:
       - يتم عد الأرقام بعد النقطة فقط حتى الوصول إلى 6 خانات:
         - إذا وصلت 6 خانات أو أكثر → يتم تصنيف القيمة كـ `Double` فورًا ويتم التوقف عن العد (Early Exit).
         - إذا انتهى النص قبل الوصول إلى 6 خانات → تصنّف كـ `Float`.
5. **إذا كان التوكن عبارة عن اسم متغير آخر:**
   - يتم الرجوع إلى جدول الرموز (Symbol Table) للحصول على النوع الأساسي للمتغير.
6. **أي حالة لا تطابق القواعد أعلاه** → يتم رفع خطأ `Type Inference Error` عند الترجمة.

---

## 🧪 أمثلة عملية:

```dolet
set a = "hello"       # → String
set b = 'A'           # → Char
set c = 42            # → Int
set d = 3.14159       # → Float (5 خانات بعد النقطة)
set e = 1.23456789    # → Double (8 خانات بعد النقطة، تم التوقف عند 6)
set f = true          # → Boolean
set g = otherVar      # → يعتمد على نوع otherVar الأصلي
```

---

## ⚠️ ملاحظة هامة:

بمجرد تحديد نوع المتغير عند الترجمة، لا يمكن تغييره لاحقًا.  
أي محاولة لإعادة الإسناد بقيمة من نوع مختلف تؤدي إلى خطأ:

```dolet
set x = 5
x = "hello"     # ❌ Error: تغيير نوع المتغير غير مسموح
```

---

## ✅ الفوائد:

- ⚡ **أداء سريع جدًا** يشابه تحديد النوع يدويًا.
- 🔒 **أمان نوعي صارم** يمنع التلاعب بالأنواع في وقت التشغيل.
- 🧼 **كتابة نظيفة وسلسة** مع الاحتفاظ بقوة الترجمة المبكرة.
- ⚙️ **قابلية عالية للتحسين مستقبليًا** بإضافة دعم لأنواع أكثر أو Union Types.




# اداء سريع للتحقق اذا الرقم الحالي ما بين 0 و 9 اذن هو رقم

```
fn is_token_numeric(value: &str) -> bool {
    // نتحقق إذا القيمة ليست فارغة أولًا
    if let Some(first_char) = value.chars().next() {
        // نتحقق إذا أول خانة هي رقم بين '0' و '9'
        return first_char >= '0' && first_char <= '9';
    }

    // القيمة فاضية أو لا تحتوي على أي خانة
    false
}
```




## 📌 دعم Null مع الحفاظ على نوع المتغير الأساسي – Dolet Enhanced Type Inference

### 🎯 الفكرة:

في تصميم لغة Dolet، يتم دعم القيمة `null` بشكل ذكي دون تغيير نوع المتغير إلى `Null` منفصل. بدلًا من ذلك، يُعتبر النوع الأساسي للمتغير قابلاً للاحتواء على `null`، كما في `int?`, `string?`, `bool?`, إلخ.

---

### 🔄 كيفية عمل النظام:

* **إذا تم تعيين `null` إلى متغير بدون تحديد نوع يدوي:**

  ```dolet
  set name = null
  ```

  يقوم الـ compiler بتمييز النوع كـ `unknown` مؤقتًا، ويتم تحديده لاحقًا عند أول استخدام واضح أو يتم رفع خطأ عند غموض النوع.

* **إذا تم تعيين `null` إلى متغير مع تحديد نوع صريح:**

  ```dolet
  set name: string = null
  ```

  يتم اعتبار النوع تلقائيًا كـ `string?` داخل نظام الـ Type Inference.

---

### ✅ الفوائد:

* الحفاظ على **نوع المتغير الأصلي**.
* دعم `null` بأداء عالٍ دون الحاجة إلى dynamic typing.
* نظام صارم يمنع تغييرات النوع بعد الإسناد.
* كتابة مرنة وآمنة:

  ```dolet
  if name != null:
      say "Hello, " + name
  else:
      say "No name provided"
  end
  ```

---

### 📌 أنواع البيانات التي تقبل null في Dolet:

| النوع                    | يقبل null؟ | ملاحظات                               |
| ------------------------ | ---------- | ------------------------------------- |
| `int`, `float`, `double` | ✅          | القيم العددية يمكن أن تكون غير موجودة |
| `string`, `char`         | ✅          | النصوص قد تكون غير معرفة              |
| `bool`                   | ✅          | قد يكون هناك غموض في الحالة           |
| `list`, `array`          | ✅          | قد لا يتم تهيئة القوائم بعد           |
| `struct`, `class`        | ✅          | الكائنات قد لا يتم إنشاؤها            |
| `function`               | ✅          | ممكن أن تكون function اختيارية        |
| `const`, `enum`          | ❌          | لا يمكن أن تكون null بعد التعريف      |

---

### ✳️ مقارنة مع لغات أخرى:

* **Python**: `None` يمكن إسناده لأي متغير، لكن بدون فحص نوع صارم.
* **Kotlin**: تستخدم `T?` للأنواع القابلة لـ null.
* **TypeScript**: نفس الشيء باستخدام union types مثل `string | null`.
* **Rust**: تستخدم `Option<T>`.

---

### ⚠️ قواعد إضافية:

* بمجرد تعيين نوع لمتغير، لا يمكن تغييره:

  ```dolet
  set x = 5
  x = null       # ✅ مسموح، يصبح int?
  x = "text"     # ❌ خطأ، لا يمكن تغيير النوع إلى string
  ```

---

### 🧠 التنفيذ داخل الـ Compiler:

1. يتم توليد توكن `null` مميز أثناء مرحلة الـ Lexer.
2. داخل الـ Parser، إذا لم يُحدد نوع:

   * يُعتبر النوع `unknown` حتى يظهر استخدام آخر يحدده.
3. عند تحديد النوع، يتم وسمه كـ `Nullable<T>` داخليًا.
4. عند الإسناد، يتم السماح فقط بقيم من نفس النوع أو `null`.

---

### 📌 خلاصة:

في Dolet، `null` ليس نوعًا منفصلًا، بل هو حالة ممكنة للأنواع الأساسية. هذا يوفر أداءً عاليًا، كتابة واضحة، وتحكمًا صارمًا في وقت الترجمة.






## ⚡ Hyperd Tokenizer – تسريع الـ Lexer في لغة Dolet

### 🎯 الفكرة:

الـ **Hyperd Tokenizer** هو تحسين متقدم على مرحلة الـ **Lexical Analysis (Lexer)** في Dolet. الهدف منه هو:

- تسريع تحليل التوكنات بنسبة تصل إلى 3-5x مقارنةً بالتحليل التقليدي.
- تقليل عدد التحويلات بين الأنواع (`String` → `char[]` → `Token`) باستخدام تجزئة موجهة مباشرة بالذاكرة.
- دعم استدلال النوع (`Type Inference`) مباشرة داخل الـ Tokenizer نفسه لبعض الحالات البسيطة.

---

### 🚀 مبدأ العمل:

بدلًا من استخدام محلل تسلسلي كلاسيكي يمر على كل حرف حرفًا، يعتمد **Hyperd Tokenizer** على:

1. **القراءة بالصفوف (line chunks)**: يتم تحليل كل سطر ككتلة، مع فهرسة بدايات الكلمات.
2. **مطابقة مسبقة باستخدام Trie أو Hash Table** للكلمات المحجوزة (مثل `set`, `if`, `fun`, `end`, ...).
3. **تشخيص مباشر للأنواع الأساسية** أثناء التجزئة:
   - `42` → نوع رقمي `int`
   - `"hello"` → `string`
   - `true`, `false` → `bool`
   - `3.14` → `float` أو `double` حسب العدد

---

### 🔍 أمثلة عملية:

```dolet
set x = 42
set name = "Hamzeh"
if name != null:
    say name
end









# 🚀 Hyperd Tokenizer – دعم الأداء العالي في لغة Dolet

## ✅ كيف الـ Hyperd Tokenizer يدعم مشروع Dolet السريع؟

| الميزة في Dolet            | كيف الـ Hyperd Tokenizer يدعمها؟                                                        |
|----------------------------|-------------------------------------------------------------------------------------------|
| **Type Inference**         | استخراج التوكنات بسرعة بدون تحليل زائد → سرعة تحديد النوع من أول رمز                     |
| **Null System الذكي**      | التوكن `null` يتم تمييزه مبكرًا وتوصيله للـ parser مباشرة                                 |
| **Pattern Matching**       | تجهيز التوكنات على شكل Hash-based enums لتسريع المطابقة                                  |
| **Macro Support**          | التوسعة المسبقة للماكرو تتم خلال الـ tokenization                                        |
| **Native Syscall Handling**| تحليل عبارات `native call` من خلال Token Rules خاصة                                       |
| **Compile-Time Safety**    | سرعة التحقق من التوكنات وتناسقها لتقليل الأخطاء الغامضة                                  |
| **Zero Allocation Parsing**| قراءة التوكنات دون نسخ المحتوى أو إعادة تخصيص في الذاكرة                                 |

---

## 🔥 كيف يعمل الـ Hyperd Tokenizer تقنيًا؟

### 🎯 نقاط القوة:

- **Single-pass scanning**: يمر على الكود مرة وحدة فقط.
- **Tokenization-on-the-fly**: بدون تجميع كل التوكنات أولًا، بل يتم إرسال التوكن للـ parser بمجرد توليده.
- **Zero-copy slices**: كل التوكنات مجرد إشارات (slices) من الكود الأصلي، ما بيأخذ نسخة جديدة.
- **Inline Token Lookup Table**: لتصنيف التوكنات (مثل `set`, `say`, `if`, `null`, `true`) باستخدام جدول داخلي سريع.

---

## 🧠 كيف يفيد أكثر في المستقبل؟

عند دمجه مع مراحل توليد الـ IR أو الـ Machine Code:

- ✅ لا يوجد bottleneck في مرحلة التحليل اللغوي.
- 🚀 الترجمة الكاملة تصير أسرع من Python أو Lua.
- 📈 الأداء يبقى خطي O(n) حتى مع الأكواد الكبيرة.
- 💡 يمكن تعزيزه لاحقًا بخيوط متعددة (Parallel Tokenization).

---

> هذا المكون أساسي لتحويل Dolet إلى لغة **Compile-time High-Performance** فعليًا، وتجاوز عنق الزجاجة الموجود في لغات التفسير أو الـ VM التقليدية.

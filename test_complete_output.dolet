# Complete Dolet Output System Test
# Demonstrates all working features with output

say "🚀 Dolet Complete Output System Test"
say "=" * 40

# Variables and constants
set name = "Hamzeh"
set age = 25
set score = 99.5
set active = true
const pi = 3.14159

say ""
say "=== Variables and Constants ==="
say "Name: " + name
say "Age: " + age
say "Score: " + score
say "Active: " + active
say "PI: " + pi

# Mathematical operations including modulo
set x = 15
set y = 4
say ""
say "=== Mathematical Operations ==="
say "x = " + x + ", y = " + y
say "x + y = " + (x + y)
say "x - y = " + (x - y)
say "x * y = " + (x * y)
say "x / y = " + (x / y)
say "x % y = " + (x % y)

# Comparison operations
say ""
say "=== Comparison Operations ==="
say "x == y: " + (x == y)
say "x != y: " + (x != y)
say "x > y: " + (x > y)
say "x < y: " + (x < y)

# User-defined functions
fun greet(person):
    say "Hello, " + person + "!"
end

fun add(a, b):
    return a + b
end

fun multiply(x, y):
    set result = x * y
    say "Multiplying " + x + " * " + y + " = " + result
    return result
end

say ""
say "=== User-Defined Functions ==="
greet(name)
greet("World")

set sum_result = add(10, 20)
say "add(10, 20) = " + sum_result

set mult_result = multiply(6, 7)
say "multiply(6, 7) returned: " + mult_result

# Complex expressions
set complex = (x + y) * 2 + pi
say ""
say "=== Complex Expressions ==="
say "Complex calculation: (x + y) * 2 + pi = " + complex

say ""
say "✅ All features working perfectly!"
say "🚀 Dolet output system is complete!"

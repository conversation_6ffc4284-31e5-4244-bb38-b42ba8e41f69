# Debug version of meow.dolet
set max_attempts = 2

fun process_attempts():
    say "Starting process_attempts"
    set attempt = 1
    
    say "Before while loop"
    while attempt <= max_attempts:
        say "In loop, attempt: " + attempt
        set attempt = attempt + 1
    end
    say "After while loop"
end

say "Before calling process_attempts"
process_attempts()
say "After calling process_attempts"

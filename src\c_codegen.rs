/// C Code Generation for Dolet Compiler
/// Generates C code that can be compiled to native executables

use crate::ast::{Program, Stmt, Expr, BinaryOp};
use std::collections::HashMap;
use std::fs;
use std::process::Command;

pub struct CCodeGenerator {
    output: String,
    variables: HashMap<String, String>, // variable name -> C type
    functions: HashMap<String, (Vec<String>, String)>, // function name -> (params, return_type)
    indent_level: usize,
}

impl CCodeGenerator {
    pub fn new() -> Self {
        Self {
            output: String::new(),
            variables: HashMap::new(),
            functions: HashMap::new(),
            indent_level: 0,
        }
    }

    pub fn generate(&mut self, program: &Program<'_>) -> String {
        // Generate C program header
        self.emit_line("#include <stdio.h>");
        self.emit_line("#include <stdlib.h>");
        self.emit_line("#include <string.h>");
        self.emit_line("");

        // First pass: collect function declarations
        for statement in &program.statements {
            if let Stmt::FunDecl { name, params, return_type, .. } = statement {
                let param_types: Vec<String> = params.iter()
                    .map(|p| self.get_c_type(&p.param_type.clone().unwrap_or(crate::token::DoletType::Int)))
                    .collect();
                let ret_type = self.get_c_type(return_type.as_ref().unwrap_or(&crate::token::DoletType::Int));
                self.functions.insert(name.clone(), (param_types, ret_type));
            }
        }

        // Generate function declarations
        for statement in &program.statements {
            if let Stmt::FunDecl { name, params, return_type, body } = statement {
                self.generate_function_declaration(name, params, return_type, body);
                self.emit_line("");
            }
        }

        // Generate main function
        self.emit_line("int main() {");
        self.indent_level += 1;

        // Generate main program statements (skip function declarations)
        for statement in &program.statements {
            if !matches!(statement, Stmt::FunDecl { .. }) {
                self.generate_statement(statement);
            }
        }

        self.emit_line("return 0;");
        self.indent_level -= 1;
        self.emit_line("}");

        self.output.clone()
    }

    pub fn compile_to_executable(&self, c_code: &str, output_path: &str) -> Result<(), String> {
        // Write C code to temporary file
        let c_file = format!("{}.c", output_path);
        fs::write(&c_file, c_code).map_err(|e| format!("Failed to write C file: {}", e))?;

        // Compile with GCC (if available) or fallback to other compilers
        let result = if self.try_gcc(&c_file, output_path).is_ok() {
            Ok(())
        } else if self.try_clang(&c_file, output_path).is_ok() {
            Ok(())
        } else if self.try_tcc(&c_file, output_path).is_ok() {
            Ok(())
        } else {
            Err("No C compiler found. Please install GCC, Clang, or TCC.".to_string())
        };

        // Clean up temporary C file
        let _ = fs::remove_file(&c_file);

        result
    }

    fn try_gcc(&self, c_file: &str, output_path: &str) -> Result<(), String> {
        let output = Command::new("gcc")
            .args(&["-o", output_path, c_file, "-O2"])
            .output()
            .map_err(|_| "GCC not found")?;

        if output.status.success() {
            Ok(())
        } else {
            Err(format!("GCC compilation failed: {}", String::from_utf8_lossy(&output.stderr)))
        }
    }

    fn try_clang(&self, c_file: &str, output_path: &str) -> Result<(), String> {
        let output = Command::new("clang")
            .args(&["-o", output_path, c_file, "-O2"])
            .output()
            .map_err(|_| "Clang not found")?;

        if output.status.success() {
            Ok(())
        } else {
            Err(format!("Clang compilation failed: {}", String::from_utf8_lossy(&output.stderr)))
        }
    }

    fn try_tcc(&self, c_file: &str, output_path: &str) -> Result<(), String> {
        let output = Command::new("tcc")
            .args(&["-o", output_path, c_file])
            .output()
            .map_err(|_| "TCC not found")?;

        if output.status.success() {
            Ok(())
        } else {
            Err(format!("TCC compilation failed: {}", String::from_utf8_lossy(&output.stderr)))
        }
    }

    fn generate_function_declaration(&mut self, name: &str, params: &[crate::ast::Parameter], return_type: &Option<crate::token::DoletType>, body: &[Stmt]) {
        let ret_type = self.get_c_type(return_type.as_ref().unwrap_or(&crate::token::DoletType::Int));
        
        // Function signature
        let mut signature = format!("{} {}(", ret_type, name);
        if params.is_empty() {
            signature.push_str("void");
        } else {
            let param_strs: Vec<String> = params.iter()
                .map(|p| format!("{} {}",
                    self.get_c_type(&p.param_type.clone().unwrap_or(crate::token::DoletType::Int)),
                    p.name))
                .collect();
            signature.push_str(&param_strs.join(", "));
        }
        signature.push_str(") {");
        self.emit_line(&signature);

        self.indent_level += 1;
        for stmt in body {
            self.generate_statement(stmt);
        }
        self.indent_level -= 1;
        self.emit_line("}");
    }

    fn generate_statement(&mut self, stmt: &Stmt) {
        match stmt {
            Stmt::VarDecl { name, type_annotation, initializer, is_const } => {
                let c_type = if let Some(t) = type_annotation {
                    self.get_c_type(t)
                } else if let Some(expr) = initializer {
                    self.infer_type_from_expr(expr)
                } else {
                    "int".to_string()
                };

                self.variables.insert(name.clone(), c_type.clone());

                if let Some(expr) = initializer {
                    let value = self.generate_expression(expr);
                    if *is_const {
                        self.emit_line(&format!("const {} {} = {};", c_type, name, value));
                    } else {
                        self.emit_line(&format!("{} {} = {};", c_type, name, value));
                    }
                } else {
                    self.emit_line(&format!("{} {};", c_type, name));
                }
            }

            Stmt::Say(expr) => {
                let value = self.generate_expression(expr);
                // Determine the appropriate printf format
                let format = match expr {
                    Expr::String(_) => "printf(\"%s\\n\", %s);",
                    Expr::Integer(_) => "printf(\"%lld\\n\", (long long)%s);",
                    Expr::Float(_) => "printf(\"%f\\n\", %s);",
                    Expr::Double(_) => "printf(\"%f\\n\", %s);",
                    Expr::Boolean(_) => "printf(\"%s\\n\", %s ? \"true\" : \"false\");",
                    _ => "printf(\"%s\\n\", %s);", // Default to string
                };
                self.emit_line(&format.replace("%s", &value));
            }

            Stmt::Return(expr) => {
                if let Some(expr) = expr {
                    let value = self.generate_expression(expr);
                    self.emit_line(&format!("return {};", value));
                } else {
                    self.emit_line("return;");
                }
            }

            Stmt::Expression(expr) => {
                let value = self.generate_expression(expr);
                self.emit_line(&format!("{};", value));
            }

            _ => {
                // Skip unsupported statements for now
                self.emit_line("// Unsupported statement");
            }
        }
    }

    fn generate_expression(&mut self, expr: &Expr) -> String {
        match expr {
            Expr::Integer(n) => n.to_string(),
            Expr::Float(f) => format!("{}f", f),
            Expr::Double(d) => d.to_string(),
            Expr::String(s) => format!("\"{}\"", s.replace("\"", "\\\"")),
            Expr::Boolean(b) => if *b { "1" } else { "0" }.to_string(),
            Expr::Identifier(name) => name.clone(),

            Expr::Binary { left, operator, right } => {
                let left_val = self.generate_expression(left);
                let right_val = self.generate_expression(right);
                let op = match operator {
                    BinaryOp::Add => "+",
                    BinaryOp::Subtract => "-",
                    BinaryOp::Multiply => "*",
                    BinaryOp::Divide => "/",
                    BinaryOp::Modulo => "%",
                    BinaryOp::Equal => "==",
                    BinaryOp::NotEqual => "!=",
                    BinaryOp::Less => "<",
                    BinaryOp::Greater => ">",
                    BinaryOp::LessEqual => "<=",
                    BinaryOp::GreaterEqual => ">=",
                    _ => "/* unsupported op */",
                };
                format!("({} {} {})", left_val, op, right_val)
            }

            Expr::Call { callee, args } => {
                if let Expr::Identifier(func_name) = callee {
                    let arg_strs: Vec<String> = args.iter()
                        .map(|arg| self.generate_expression(arg))
                        .collect();
                    format!("{}({})", func_name, arg_strs.join(", "))
                } else {
                    "/* invalid call */".to_string()
                }
            }

            _ => "/* unsupported expr */".to_string(),
        }
    }

    fn get_c_type(&self, dolet_type: &crate::token::DoletType) -> String {
        match dolet_type {
            crate::token::DoletType::Int => "long long".to_string(),
            crate::token::DoletType::Float => "float".to_string(),
            crate::token::DoletType::Double => "double".to_string(),
            crate::token::DoletType::String => "char*".to_string(),
            crate::token::DoletType::Bool => "int".to_string(),
            crate::token::DoletType::Char => "char".to_string(),
            crate::token::DoletType::Null => "void".to_string(),
            _ => "int".to_string(), // Default fallback
        }
    }

    fn infer_type_from_expr(&self, expr: &Expr) -> String {
        match expr {
            Expr::Integer(_) => "long long".to_string(),
            Expr::Float(_) => "float".to_string(),
            Expr::Double(_) => "double".to_string(),
            Expr::String(_) => "char*".to_string(),
            Expr::Boolean(_) => "int".to_string(),
            Expr::Char(_) => "char".to_string(),
            _ => "int".to_string(),
        }
    }

    fn emit_line(&mut self, line: &str) {
        for _ in 0..self.indent_level {
            self.output.push_str("    ");
        }
        self.output.push_str(line);
        self.output.push('\n');
    }
}

use std::io::{self, Write};

fn is_even(number: i64) -> bool {
    if ((number % 2) == 0) {
        return true;
    } else {
        return false;
    }
    false
}

fn process_attempts() {
    let mut attempt = 1;
    while (attempt <= MAX_ATTEMPTS) {
        println!("{}", format!("{}{}" , "Attempt: ", attempt));
        random_number = (attempt * 3);
        if is_even(random_number) {
            println!("{}", format!("{}{}" , format!("{}{}" , "The number ", random_number), " is even."));
        } else {
            if (random_number > 10) {
                println!("{}", format!("{}{}" , format!("{}{}" , "The number ", random_number), " is odd and greater than 10."));
            } else {
                println!("{}", format!("{}{}" , format!("{}{}" , "The number ", random_number), " is odd and less than or equal to 10."));
            }
        }
        attempt = (attempt + 1);
    }
}

const MAX_ATTEMPTS: i64 = 5;

fn main() {
    let mut max_attempts = 5;
    process_attempts();
}

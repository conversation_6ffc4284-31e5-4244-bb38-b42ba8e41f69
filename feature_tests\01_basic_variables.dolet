# Basic Variables Test
# Testing basic variable declarations and assignments

# Integer variables
set small_int = 42
set large_int = 999999
set negative_int = -123

# Float variables
set simple_float = 3.14
set precise_float = 2.71828

# Double variables
set very_precise_double = 3.141592653589793
set scientific_notation = 1.23456789

# Boolean variables
set is_testing = true
set is_complete = false

# String variables
set program_name = "Dolet Basic Variables Test"
set title = "Test Window"

# Null value
set optional_data = null

# Output results
say "========================================="
say "Basic Variables Test"
say "========================================="

say "Integer Variables:"
say "small_int = " + small_int
say "large_int = " + large_int
say "negative_int = " + negative_int

say ""
say "Float Variables:"
say "simple_float = " + simple_float
say "precise_float = " + precise_float

say ""
say "Double Variables:"
say "very_precise_double = " + very_precise_double
say "scientific_notation = " + scientific_notation

say ""
say "Boolean Variables:"
say "is_testing = " + is_testing
say "is_complete = " + is_complete

say ""
say "String Variables:"
say "program_name = " + program_name
say "title = " + title

say ""
say "Null Variable:"
say "optional_data = " + optional_data

say "========================================="

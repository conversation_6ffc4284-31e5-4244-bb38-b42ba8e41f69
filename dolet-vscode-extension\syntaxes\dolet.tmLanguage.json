{"scopeName": "source.dolet", "patterns": [{"name": "keyword.control.dolet", "match": "\\b(fun|set|const|start|end|if|else|for|while|match|case|default|class|struct|import|action|then|do|stop|into|return)\\b"}, {"name": "support.function.builtin.dolet", "match": "\\b(say|speak|ask|read|write|print|play_sound|show|wait|move_up|move_down|quit)\\b"}, {"name": "variable.language.self.dolet", "match": "\\bself\\b"}, {"name": "constant.numeric.dolet", "match": "\\b\\d+(\\.\\d+)?\\b"}, {"name": "variable.other.assignment.dolet", "match": "(?<=\\b(set|into)\\s)([a-zA-Z_][a-zA-Z0-9_]*)"}, {"name": "string.quoted.double.dolet", "begin": "\"", "end": "\""}, {"name": "comment.line.number-sign.dolet", "match": "#.*$"}]}
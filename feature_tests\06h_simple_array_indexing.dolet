# Simple Array Indexing Test
# Testing array element access

set numbers = [10, 20, 30, 40, 50]
set names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
set temperatures = [20.5, 22.0, 19.8, 25.3, 18.7]

say "========================================"
say "Array Indexing Test"
say "========================================"

# Test array indexing
say "Array Element Access:"
say "numbers[0] = " + numbers[0]
say "numbers[2] = " + numbers[2]
say "numbers[4] = " + numbers[4]

say ""
say "names[0] = " + names[0]
say "names[1] = " + names[1]
say "names[3] = " + names[3]

say ""
say "temperatures[0] = " + temperatures[0]
say "temperatures[3] = " + temperatures[3]
say "temperatures[4] = " + temperatures[4]

say "========================================"
say "Array indexing works like any language!"
say "========================================"

/// Cache-Optimized Parser for Ultra-Fast AST Generation
/// Uses memory layout optimizations and prefetching for maximum performance

use crate::ast::{Program, Stmt, Expr};
use crate::token::{Token, TokenKind};
use bumpalo::Bump;

pub struct CacheOptimizedParser<'a, 'arena> {
    tokens: &'a [Token<'a>],
    current: usize,
    arena: &'arena Bump,
    // Cache-friendly token buffer for lookahead
    token_cache: [Option<&'a Token<'a>>; 8],
    cache_start: usize,
}

impl<'a, 'arena> CacheOptimizedParser<'a, 'arena> {
    pub fn new(tokens: &'a [Token<'a>], arena: &'arena Bump) -> Self {
        let mut parser = Self {
            tokens,
            current: 0,
            arena,
            token_cache: [None; 8],
            cache_start: 0,
        };
        
        // Pre-fill cache for better performance
        parser.fill_cache();
        parser
    }

    pub fn parse(&mut self) -> Result<Program<'arena>, Vec<String>> {
        let mut statements = Vec::new();
        let mut errors = Vec::new();

        while !self.is_at_end() {
            match self.parse_statement() {
                Ok(stmt) => {
                    if let Some(stmt) = stmt {
                        statements.push(stmt);
                    }
                }
                Err(err) => {
                    errors.push(err);
                    self.synchronize();
                }
            }
        }

        if errors.is_empty() {
            Ok(Program { statements })
        } else {
            Err(errors)
        }
    }

    fn fill_cache(&mut self) {
        for i in 0..8 {
            let token_index = self.cache_start + i;
            if token_index < self.tokens.len() {
                self.token_cache[i] = Some(&self.tokens[token_index]);
            } else {
                self.token_cache[i] = None;
            }
        }
    }

    fn advance_cache(&mut self) {
        // Shift cache left and fill new slot
        for i in 0..7 {
            self.token_cache[i] = self.token_cache[i + 1];
        }
        
        let new_token_index = self.cache_start + 7;
        if new_token_index < self.tokens.len() {
            self.token_cache[7] = Some(&self.tokens[new_token_index]);
        } else {
            self.token_cache[7] = None;
        }
        
        self.cache_start += 1;
    }

    fn peek_cached(&self, offset: usize) -> Option<&Token<'a>> {
        if offset < 8 {
            self.token_cache[offset].copied()
        } else {
            // Fallback for deep lookahead
            let token_index = self.current + offset;
            if token_index < self.tokens.len() {
                Some(&self.tokens[token_index])
            } else {
                None
            }
        }
    }

    fn parse_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        // Fast path for common statement types using cache
        if let Some(token) = self.peek_cached(0) {
            match token.kind {
                TokenKind::Set => self.parse_var_declaration(),
                TokenKind::Say => self.parse_say_statement(),
                TokenKind::If => self.parse_if_statement(),
                TokenKind::While => self.parse_while_statement(),
                TokenKind::LeftBrace => self.parse_block_statement(),
                TokenKind::Semicolon => {
                    self.advance();
                    Ok(None) // Empty statement
                }
                _ => self.parse_expression_statement(),
            }
        } else {
            Ok(None)
        }
    }

    fn parse_var_declaration(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        self.advance(); // consume 'set'

        let name_token = self.advance()
            .ok_or_else(|| "Expected variable name".to_string())?;
        
        if name_token.kind != TokenKind::Identifier {
            return Err("Expected variable name".to_string());
        }

        let name = name_token.lexeme.to_string();

        if !self.match_token(TokenKind::Equal) {
            return Err("Expected '=' after variable name".to_string());
        }

        let initializer = self.parse_expression()?;

        // Optional semicolon
        self.match_token(TokenKind::Semicolon);

        let stmt = self.arena.alloc(Stmt::VarDecl {
            name,
            type_annotation: None,
            initializer: Some(initializer),
            is_const: false,
        });

        Ok(Some(stmt))
    }

    fn parse_say_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        self.advance(); // consume 'say'

        let expr = self.parse_expression()?;

        // Optional semicolon
        self.match_token(TokenKind::Semicolon);

        let stmt = self.arena.alloc(Stmt::Say(expr));
        Ok(Some(stmt))
    }

    fn parse_if_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        self.advance(); // consume 'if'

        let condition = self.parse_expression()?;
        let then_branch = self.parse_statement()?.unwrap_or_else(|| {
            self.arena.alloc(Stmt::Expression(self.arena.alloc(Expr::Null)))
        });

        let else_branch = if self.match_token(TokenKind::Else) {
            Some(self.parse_statement()?.unwrap_or_else(|| {
                self.arena.alloc(Stmt::Expression(self.arena.alloc(Expr::Null)))
            }))
        } else {
            None
        };

        let stmt = self.arena.alloc(Stmt::If {
            condition,
            then_branch,
            else_branch,
        });

        Ok(Some(stmt))
    }

    fn parse_while_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        self.advance(); // consume 'while'

        let condition = self.parse_expression()?;
        let body = self.parse_statement()?.unwrap_or_else(|| {
            self.arena.alloc(Stmt::Expression(self.arena.alloc(Expr::Null)))
        });

        let stmt = self.arena.alloc(Stmt::While { condition, body });
        Ok(Some(stmt))
    }

    fn parse_block_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        self.advance(); // consume '{'

        let mut statements = Vec::new();

        while !self.check(TokenKind::RightBrace) && !self.is_at_end() {
            if let Some(stmt) = self.parse_statement()? {
                statements.push(stmt);
            }
        }

        if !self.match_token(TokenKind::RightBrace) {
            return Err("Expected '}' after block".to_string());
        }

        let stmt = self.arena.alloc(Stmt::Block { statements });
        Ok(Some(stmt))
    }

    fn parse_expression_statement(&mut self) -> Result<Option<&'arena Stmt<'arena>>, String> {
        let expr = self.parse_expression()?;
        self.match_token(TokenKind::Semicolon);
        
        let stmt = self.arena.alloc(Stmt::Expression(expr));
        Ok(Some(stmt))
    }

    fn parse_expression(&mut self) -> Result<&'arena Expr<'arena>, String> {
        self.parse_logical_or()
    }

    fn parse_logical_or(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_logical_and()?;

        while self.match_token(TokenKind::Or) {
            let right = self.parse_logical_and()?;
            expr = self.arena.alloc(Expr::Logical {
                left: expr,
                operator: crate::ast::LogicalOp::Or,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_logical_and(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_equality()?;

        while self.match_token(TokenKind::And) {
            let right = self.parse_equality()?;
            expr = self.arena.alloc(Expr::Logical {
                left: expr,
                operator: crate::ast::LogicalOp::And,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_equality(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_comparison()?;

        while let Some(token) = self.peek_cached(0) {
            let op = match token.kind {
                TokenKind::BangEqual => crate::ast::BinaryOp::NotEqual,
                TokenKind::EqualEqual => crate::ast::BinaryOp::Equal,
                _ => break,
            };

            self.advance();
            let right = self.parse_comparison()?;
            expr = self.arena.alloc(Expr::Binary {
                left: expr,
                operator: op,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_comparison(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_term()?;

        while let Some(token) = self.peek_cached(0) {
            let op = match token.kind {
                TokenKind::Greater => crate::ast::BinaryOp::Greater,
                TokenKind::GreaterEqual => crate::ast::BinaryOp::GreaterEqual,
                TokenKind::Less => crate::ast::BinaryOp::Less,
                TokenKind::LessEqual => crate::ast::BinaryOp::LessEqual,
                _ => break,
            };

            self.advance();
            let right = self.parse_term()?;
            expr = self.arena.alloc(Expr::Binary {
                left: expr,
                operator: op,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_term(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_factor()?;

        while let Some(token) = self.peek_cached(0) {
            let op = match token.kind {
                TokenKind::Minus => crate::ast::BinaryOp::Subtract,
                TokenKind::Plus => crate::ast::BinaryOp::Add,
                _ => break,
            };

            self.advance();
            let right = self.parse_factor()?;
            expr = self.arena.alloc(Expr::Binary {
                left: expr,
                operator: op,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_factor(&mut self) -> Result<&'arena Expr<'arena>, String> {
        let mut expr = self.parse_unary()?;

        while let Some(token) = self.peek_cached(0) {
            let op = match token.kind {
                TokenKind::Slash => crate::ast::BinaryOp::Divide,
                TokenKind::Star => crate::ast::BinaryOp::Multiply,
                TokenKind::Percent => crate::ast::BinaryOp::Modulo,
                _ => break,
            };

            self.advance();
            let right = self.parse_unary()?;
            expr = self.arena.alloc(Expr::Binary {
                left: expr,
                operator: op,
                right,
            });
        }

        Ok(expr)
    }

    fn parse_unary(&mut self) -> Result<&'arena Expr<'arena>, String> {
        if let Some(token) = self.peek_cached(0) {
            match token.kind {
                TokenKind::Bang => {
                    self.advance();
                    let right = self.parse_unary()?;
                    Ok(self.arena.alloc(Expr::Unary {
                        operator: crate::ast::UnaryOp::Not,
                        operand: right,
                    }))
                }
                TokenKind::Minus => {
                    self.advance();
                    let right = self.parse_unary()?;
                    Ok(self.arena.alloc(Expr::Unary {
                        operator: crate::ast::UnaryOp::Negate,
                        operand: right,
                    }))
                }
                _ => self.parse_primary(),
            }
        } else {
            Err("Unexpected end of input".to_string())
        }
    }

    fn parse_primary(&mut self) -> Result<&'arena Expr<'arena>, String> {
        if let Some(token) = self.advance() {
            match token.kind {
                TokenKind::True => Ok(self.arena.alloc(Expr::Boolean(true))),
                TokenKind::False => Ok(self.arena.alloc(Expr::Boolean(false))),
                TokenKind::Null => Ok(self.arena.alloc(Expr::Null)),
                TokenKind::Integer => {
                    let value = token.lexeme.parse::<i64>()
                        .map_err(|_| "Invalid integer literal".to_string())?;
                    Ok(self.arena.alloc(Expr::Integer(value)))
                }
                TokenKind::Double => {
                    let value = token.lexeme.parse::<f64>()
                        .map_err(|_| "Invalid double literal".to_string())?;
                    Ok(self.arena.alloc(Expr::Double(value)))
                }
                TokenKind::String => {
                    let value = &token.lexeme[1..token.lexeme.len()-1]; // Remove quotes
                    Ok(self.arena.alloc(Expr::String(value.to_string())))
                }
                TokenKind::Char => {
                    let value = token.lexeme.chars().nth(1)
                        .ok_or_else(|| "Invalid char literal".to_string())?;
                    Ok(self.arena.alloc(Expr::Char(value)))
                }
                TokenKind::Identifier => {
                    Ok(self.arena.alloc(Expr::Identifier(token.lexeme)))
                }
                TokenKind::LeftParen => {
                    let expr = self.parse_expression()?;
                    if !self.match_token(TokenKind::RightParen) {
                        return Err("Expected ')' after expression".to_string());
                    }
                    Ok(expr)
                }
                _ => Err(format!("Unexpected token: {:?}", token.kind)),
            }
        } else {
            Err("Unexpected end of input".to_string())
        }
    }

    fn advance(&mut self) -> Option<&Token<'a>> {
        if !self.is_at_end() {
            let token = self.peek_cached(0);
            self.current += 1;
            self.advance_cache();
            token
        } else {
            None
        }
    }

    fn match_token(&mut self, kind: TokenKind) -> bool {
        if self.check(kind) {
            self.advance();
            true
        } else {
            false
        }
    }

    fn check(&self, kind: TokenKind) -> bool {
        if let Some(token) = self.peek_cached(0) {
            token.kind == kind
        } else {
            false
        }
    }

    fn is_at_end(&self) -> bool {
        self.current >= self.tokens.len() || 
        self.peek_cached(0).map_or(true, |t| t.kind == TokenKind::Eof)
    }

    fn synchronize(&mut self) {
        self.advance();

        while !self.is_at_end() {
            if let Some(token) = self.peek_cached(0) {
                match token.kind {
                    TokenKind::Set | TokenKind::Say | TokenKind::If | 
                    TokenKind::While | TokenKind::For | TokenKind::Fn | 
                    TokenKind::Return => break,
                    _ => {
                        self.advance();
                    }
                }
            } else {
                break;
            }
        }
    }
}

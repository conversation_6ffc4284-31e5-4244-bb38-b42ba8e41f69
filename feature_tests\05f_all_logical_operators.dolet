# All Logical Operators Test
# Testing and, or, not operators like any programming language

set age = 25
set is_student = true
set has_license = false
set score = 85

say "========================================"
say "All Logical Operators Test"
say "========================================"

# Test 1: AND operator
if age >= 18 and is_student == true:
    say "✅ AND operator: Adult student detected"
end

# Test 2: OR operator
if age >= 30 or is_student == true:
    say "✅ OR operator: Either adult or student"
end

# Test 3: NOT operator
if not has_license == true:
    say "✅ NOT operator: No license detected"
end

# Test 4: Combined logical operators
if age >= 18 and score >= 80:
    say "✅ Combined AND: Qualified adult student"
end

if score >= 90 or age >= 25:
    say "✅ Combined OR: High score or mature age"
end

say "========================================"
say "All logical operators working perfectly!"
say "This works like any programming language!"
say "========================================"

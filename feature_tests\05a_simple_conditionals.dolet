# Simple Conditional Statements Test
# Testing basic if/else statements without logical operators

set age = 25
set score = 85
set is_student = true
set temperature = 22.5

# Simple if statement
if age >= 18:
    say "You are an adult"
end

# If-else statement
if score >= 90:
    say "Grade: A"
else:
    say "Grade: B or lower"
end

# Nested if statements
if is_student == true:
    if score >= 80:
        say "Good student with high score"
    else:
        say "Student needs improvement"
    end
else:
    say "Not a student"
end

# Comparison operators
if age == 25:
    say "Age is exactly 25"
end

if score != 100:
    say "Score is not perfect"
end

if temperature <= 25.0:
    say "Temperature is comfortable"
end

say "========================================="
say "Simple Conditional Statements Test Complete"
say "========================================="

# Medium complexity test to find the performance bottleneck

# Variables with different types
set program_name = "Medium Test"
set version = 2.0
set test_count = 10
set pi = 3.141592653589793
set is_testing = true

# Arrays
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]

# Functions
fun add(a: int, b: int) -> int:
    return a + b
end

fun multiply(a: float, b: float) -> float:
    return a * b
end

fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

# Complex expressions
set sum = add(10, 20)
set product = multiply(3.14, 2.0)
set fact5 = factorial(5)
set complex_calc = sum + product * pi

# Conditional logic
if version >= 2.0:
    say "Version 2.0 or higher"
    if is_testing == true:
        say "Testing mode active"
        set test_count = test_count + 1
    else:
        say "Production mode"
    end
else:
    say "Older version"
end

# More calculations
set area = pi * multiply(5.0, 5.0)
set volume = area * 10.0
set ratio = volume / area

# Output results
say program_name + " v" + version
say "Sum: " + sum
say "Product: " + product
say "Factorial: " + fact5
say "Complex calculation: " + complex_calc
say "Area: " + area
say "Volume: " + volume
say "Ratio: " + ratio
say "Test count: " + test_count

say "Medium test completed successfully!"

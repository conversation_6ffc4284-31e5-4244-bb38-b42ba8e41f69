# Test 1: Loop accumulation problem - should calculate sum correctly
set sum = 0
for i from 1 to 10:
    set sum = sum + i
end
say sum

# Test 2: Function with parameters - should work correctly
fun greet(person):
    say "Hello, " + person + "!"
end
greet("World")

# Test 3: Variable reassignment without 'set' - should work
set x = 10
set y = 5
x = 5
say "x + y = " + (x + y)

# Test 4: String concatenation order dependency - both should work
set name1 = "Dolet"
set version1 = 2.0
say "Welcome to " + version1 + " version " + name1

set name2 = "Dolet"  
set version2 = 2.0
say "Welcome to " + name2 + " version " + version2

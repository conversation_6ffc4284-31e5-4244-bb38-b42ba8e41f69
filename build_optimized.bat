@echo off
REM Ultra-optimized Dolet Compiler Build Script
REM Builds the fastest possible dolet.exe with all optimizations enabled

echo Building Ultra-Optimized Dolet Compiler...
echo.

REM Clean previous builds
echo Cleaning previous builds...
cargo clean

REM Build with maximum optimizations
echo Building with ultra-performance optimizations...
set RUSTFLAGS=-C target-cpu=native

REM Build the release version with all performance features
cargo build --release --features ultra --bin dolet

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM Copy the executable to root directory for easy access
echo Copying dolet.exe to root directory...
copy "target\release\dolet.exe" "dolet.exe"

REM Show file size and info
echo.
echo ✅ Build completed successfully!
echo.
echo Executable info:
dir dolet.exe
echo.

REM Test compilation speed
echo Testing compilation speed...
echo.
dolet.exe examples\main.dolet --time

echo.
echo 🚀 Ultra-optimized dolet.exe is ready!
echo Usage: dolet.exe your_file.dolet [options]
echo.
pause

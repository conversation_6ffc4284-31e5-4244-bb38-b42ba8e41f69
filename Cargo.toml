[package]
name = "dolet-compiler"
version = "0.1.0"
edition = "2021"
authors = ["Dolet Team"]
description = "High-performance Dolet language compiler with direct machine code generation"
license = "MIT"

[dependencies]
# Ultra-lightweight dependencies for maximum performance
bumpalo = "3.14"         # Fast bump allocator for AST nodes (essential for speed)

# Optional heavy dependencies (only when needed)
memmap2 = { version = "0.9", optional = true }
rayon = { version = "1.8", optional = true }
hashbrown = { version = "0.14", optional = true }
cranelift = { version = "0.102", optional = true }
cranelift-module = { version = "0.102", optional = true }
cranelift-jit = { version = "0.102", optional = true }

# SIMD and ultra-performance dependencies
wide = { version = "0.7", optional = true }  # SIMD operations
bytemuck = { version = "1.14", optional = true }  # Zero-copy casting

[profile.release]
# Maximum performance optimizations
opt-level = 3
lto = "fat"              # Link-time optimization
codegen-units = 1        # Single codegen unit for better optimization
panic = "abort"          # Smaller binary size
strip = true             # Remove debug symbols
overflow-checks = false  # Disable overflow checks for maximum speed
debug-assertions = false # Disable debug assertions

[profile.ultra-release]
# Ultra-performance profile for maximum speed
inherits = "release"
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
overflow-checks = false
debug-assertions = false

[profile.dev]
# Fast compilation for development
opt-level = 1
debug = true

[[bin]]
name = "doletc"
path = "src/main.rs"

[[bin]]
name = "dolet"
path = "src/main.rs"

[features]
default = ["direct", "simd"]  # Direct machine code + SIMD by default (fastest)
direct = []              # Direct machine code generation (zero dependencies)
jit = ["cranelift", "cranelift-module", "cranelift-jit"]  # JIT compilation
parallel = ["rayon", "hashbrown"]     # Parallel compilation
mmap = ["memmap2"]       # Memory-mapped I/O
simd = ["wide", "bytemuck"]  # SIMD optimizations for ultra-fast processing
ultra = ["direct", "simd", "parallel", "mmap"]  # Ultra-performance mode
full = ["jit", "parallel", "mmap", "simd"]  # All features enabled

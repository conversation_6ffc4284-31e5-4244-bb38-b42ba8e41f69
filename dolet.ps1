# Dolet Compiler - Ultra-fast Programming Language
# Usage: .\dolet.ps1 <source_file.dolet> [options]

param(
    [Parameter(Position=0)]
    [string]$SourceFile,
    
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$RemainingArgs
)

if ($args.Count -eq 0 -and [string]::IsNullOrEmpty($SourceFile)) {
    Write-Host "Dolet Compiler - Ultra-fast Programming Language"
    Write-Host "Usage: .\dolet.ps1 <source_file.dolet> [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  --ast        Print the Abstract Syntax Tree"
    Write-Host "  --tokens     Print the token stream"
    Write-Host "  --time       Show compilation timing"
    Write-Host "  --help       Show this help message"
    exit 1
}

# Run the Dolet compiler
cargo run --bin dolet --features direct --release -- $args

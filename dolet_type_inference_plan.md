# خطة تحسين الأداء في نظام استنتاج النوع للغة Dolet

## ✅ تحليل معماري داخلي

### المكونات الرئيسية:

1. **Lexer**

   * يقوم بتقسيم الكود إلى رموز (Tokens) بسرعة باستخدام خوارزمية DFA.
   * يقوم بتمرير أول رمز بعد `=` مباشرة إلى وحدة استنتاج النوع.

2. **Token Inference Engine**

   * خوارزمية خفيفة الوزن يتم تنفيذها في `lexer.rs` لتحديد النوع دون الحاجة لـ parser.
   * يدعم التوقف المبكر (early exit) عند الوصول إلى تأكيد لنوع `Double` أو `Boolean`.

3. **Parser**

   * يستخدم فقط لتركيب الشيفرة وليس لتحليل الأنواع.

4. **Symbol Table**

   * هيكل HashMap يربط أسماء المتغيرات بأنواعها.
   * يُستخدم عندما يكون التوكن اسم متغير آخر.

5. **Error Reporter**

   * مسؤول عن طباعة رسائل خطأ الترجمة عندما يتعذر استنتاج النوع.

6. **Type Locking System**

   * يمنع أي محاولة لتغيير نوع المتغير بعد تحديده.

---

## ✅ خوارزمية استنتاج النوع التفصيلية

1. يبدأ التحليل عند أول توكن بعد `=`.
2. يتم تطبيق القواعد التالية بترتيب واضح:

   * إذا بدأ بـ `"` → `String`
   * إذا بدأ بـ `'` → `Char`
   * إذا كان `true` أو `false` → `Boolean`
   * إذا بدأ برقم:

     * بدون نقطة → `Int`
     * مع نقطة:

       * عد الأرقام بعد النقطة حتى 6 خانات

         * إذا وصل 6 → `Double`
         * أقل من 6 → `Float`
   * إذا كان اسم متغير آخر → الرجوع لـ `Symbol Table`
   * غير ذلك → خطأ في الاستنتاج

---

## ✅ استراتيجيات أداء عالي

| التقنية                 | شرح                                                        |
| ----------------------- | ---------------------------------------------------------- |
| **Early Exit**          | التوقف عن تحليل الرقم بمجرد تأكيد نوع `Double`.            |
| **Zero-Copy**           | استخدام String slices بدون نسخ.                            |
| **Inlining**            | توجيه Rust لوضع دوال التحقق كـ inline لتحسين الأداء.       |
| **Static Allocation**   | استخدام stack متغيرات عند الإمكان لتقليل heap allocations. |
| **Compile-Time Guards** | حماية ضد تغيير النوع بعد الترجمة.                          |

---

## ✅ خطة اختبار وقياس الأداء

| الاختبار            | الهدف                                             |
| ------------------- | ------------------------------------------------- |
| Benchmark set a = 5 | التأكد من الأداء في 10M عملية.                    |
| Memory Profiling    | تحليل إدارة الذاكرة.                              |
| مقارنة مع Rust      | تقييم سرعة استنتاج النوع مقارنة بـ Rust native.   |
| Stress Testing      | ملفات ضخمة تحتوي آلاف المتغيرات للتحقق من الثبات. |

---

## ✅ كود Rust توضيحي داخلي

```rust
fn infer_type(token: &str) -> Result<Type, InferenceError> {
    if token.starts_with('"') {
        Ok(Type::String)
    } else if token.starts_with('\'') {
        Ok(Type::Char)
    } else if token == "true" || token == "false" {
        Ok(Type::Boolean)
    } else if is_token_numeric(token) {
        if token.contains('.') {
            let after_dot = token.split('.').nth(1).unwrap_or("");
            if after_dot.len() >= 6 {
                Ok(Type::Double)
            } else {
                Ok(Type::Float)
            }
        } else {
            Ok(Type::Int)
        }
    } else if let Some(existing_type) = SYMBOL_TABLE.get(token) {
        Ok(existing_type.clone())
    } else {
        Err(InferenceError::UnknownType)
    }
}
```

---

## 🧭 ملاحظات مستقبلية

* يمكن دعم `Array<T>` في استنتاج النوع لاحقًا.
* يمكن استخدام `Union` types باستخدام صيغة `type result = Int | String`.
* دراسة دعم الـ `Type Macros` لتحويلات ذكية.
* إضافة caching لتكرار قيم التوكنات المتكررة.
* إنشاء DSL داخلي لمساعدة الـ Parser على التعامل مع أنواع جديدة بسرعة.

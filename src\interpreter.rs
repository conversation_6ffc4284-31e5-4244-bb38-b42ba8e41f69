/// Simple Dolet Interpreter for immediate output and testing
/// This provides a fast way to see results without complex machine code generation

use crate::ast::{Program, Stmt, Expr, BinaryOp, UnaryOp};
use crate::token::DoletType;
use std::collections::HashMap;
use std::io::{self, Write};

#[derive(Debug, <PERSON>lone)]
pub enum Value {
    Integer(i64),
    Float(f32),
    Double(f64),
    String(String),
    Char(char),
    <PERSON><PERSON><PERSON>(bool),
    Null,
}

impl Value {
    pub fn to_string(&self) -> String {
        match self {
            Value::Integer(n) => n.to_string(),
            Value::Float(f) => f.to_string(),
            Value::Double(d) => d.to_string(),
            Value::String(s) => s.clone(),
            Value::Char(c) => c.to_string(),
            Value::Boolean(b) => b.to_string(),
            Value::Null => "null".to_string(),
        }
    }

    pub fn is_truthy(&self) -> bool {
        match self {
            Value::Boolean(b) => *b,
            Value::Null => false,
            Value::Integer(n) => *n != 0,
            Value::Float(f) => *f != 0.0,
            Value::Double(d) => *d != 0.0,
            Value::String(s) => !s.is_empty(),
            Value::Char(_) => true,
        }
    }
}

pub struct Interpreter<'a> {
    variables: HashMap<String, Value>,
    output: Vec<String>,
    functions: HashMap<String, (Vec<String>, Vec<&'a Stmt<'a>>)>, // (params, body)
}

impl<'a> Interpreter<'a> {
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            output: Vec::new(),
            functions: HashMap::new(),
        }
    }

    pub fn interpret(&mut self, program: &'a Program<'a>) -> Result<(), String> {
        // First pass: collect function definitions
        for statement in &program.statements {
            if let Stmt::FunDecl { name, params, body, .. } = statement {
                let param_names: Vec<String> = params.iter().map(|p| p.name.clone()).collect();
                self.functions.insert(name.clone(), (param_names, body.iter().collect()));
            }
        }

        // Second pass: execute statements
        for statement in &program.statements {
            self.execute_statement(statement)?;
        }
        Ok(())
    }

    pub fn get_output(&self) -> &[String] {
        &self.output
    }

    pub fn print_output(&self) {
        for line in &self.output {
            println!("{}", line);
        }
    }

    fn execute_statement(&mut self, stmt: &Stmt<'_>) -> Result<(), String> {
        match stmt {
            Stmt::VarDecl { name, initializer, .. } => {
                let value = if let Some(expr) = initializer {
                    self.evaluate_expression(expr)?
                } else {
                    Value::Null
                };
                self.variables.insert(name.clone(), value);
            }

            Stmt::FunDecl { .. } => {
                // Function declarations are handled in the first pass
                // Skip them in the execution phase
            }
            
            Stmt::Say(expr) => {
                let value = self.evaluate_expression(expr)?;
                let output = value.to_string();
                self.output.push(output.clone());
                println!("{}", output); // Immediate console output
                io::stdout().flush().unwrap(); // Ensure immediate display
            }
            
            Stmt::Expression(expr) => {
                // Evaluate expression but don't store result
                self.evaluate_expression(expr)?;
            }
            
            Stmt::CompoundAssign { target, operator, value } => {
                let current_value = self.variables.get(target)
                    .ok_or_else(|| format!("Undefined variable: {}", target))?
                    .clone();

                let new_value = self.evaluate_expression(value)?;
                let result = self.apply_binary_op(&current_value, operator, &new_value)?;
                self.variables.insert(target.clone(), result);
            }

            Stmt::For { variable, start, end, body } => {
                let start_val = self.evaluate_expression(start)?;
                let end_val = self.evaluate_expression(end)?;

                // Convert to integers for loop iteration
                let start_int = match start_val {
                    Value::Integer(n) => n,
                    Value::Float(f) => f as i64,
                    Value::Double(d) => d as i64,
                    _ => return Err("For loop start must be a number".to_string()),
                };

                let end_int = match end_val {
                    Value::Integer(n) => n,
                    Value::Float(f) => f as i64,
                    Value::Double(d) => d as i64,
                    _ => return Err("For loop end must be a number".to_string()),
                };

                // Execute the loop
                for i in start_int..=end_int {
                    // Set the loop variable
                    self.variables.insert(variable.clone(), Value::Integer(i));

                    // Execute loop body
                    for stmt in body {
                        self.execute_statement(stmt)?;
                    }
                }
            }

            _ => {
                // For now, skip other statement types
                self.output.push(format!("[Executed: {:?}]", stmt));
            }
        }
        Ok(())
    }

    fn evaluate_expression(&mut self, expr: &Expr<'_>) -> Result<Value, String> {
        match expr {
            Expr::Integer(n) => Ok(Value::Integer(*n)),
            Expr::Float(f) => Ok(Value::Float(*f)),
            Expr::Double(d) => Ok(Value::Double(*d)),
            Expr::String(s) => Ok(Value::String(s.clone())),
            Expr::Char(c) => Ok(Value::Char(*c)),
            Expr::Boolean(b) => Ok(Value::Boolean(*b)),
            Expr::Null => Ok(Value::Null),
            
            Expr::Identifier(name) => {
                self.variables.get(name)
                    .cloned()
                    .ok_or_else(|| format!("Undefined variable: {}", name))
            }
            
            Expr::Binary { left, operator, right } => {
                let left_val = self.evaluate_expression(left)?;
                let right_val = self.evaluate_expression(right)?;
                self.apply_binary_op(&left_val, operator, &right_val)
            }
            
            Expr::Unary { operator, operand } => {
                let val = self.evaluate_expression(operand)?;
                self.apply_unary_op(operator, &val)
            }
            
            Expr::Call { callee, args } => {
                if let Expr::Identifier(func_name) = callee {
                    match func_name.as_str() {
                        "say" => {
                            if let Some(arg) = args.first() {
                                let value = self.evaluate_expression(arg)?;
                                let output = value.to_string();
                                self.output.push(output.clone());
                                println!("{}", output);
                                io::stdout().flush().unwrap();
                            }
                            Ok(Value::Null)
                        }
                        _ => {
                            // Check if it's a user-defined function
                            if let Some((params, body)) = self.functions.get(func_name).cloned() {
                                self.call_user_function(func_name, &params, &body, args)
                            } else {
                                Err(format!("Unknown function: {}", func_name))
                            }
                        }
                    }
                } else {
                    Err("Invalid function call".to_string())
                }
            }
            
            _ => {
                // For complex expressions, return a placeholder
                Ok(Value::String("[complex expression]".to_string()))
            }
        }
    }

    fn apply_binary_op(&self, left: &Value, op: &BinaryOp, right: &Value) -> Result<Value, String> {
        match (left, right) {
            (Value::Integer(a), Value::Integer(b)) => {
                match op {
                    BinaryOp::Add => Ok(Value::Integer(a + b)),
                    BinaryOp::Subtract => Ok(Value::Integer(a - b)),
                    BinaryOp::Multiply => Ok(Value::Integer(a * b)),
                    BinaryOp::Divide => {
                        if *b == 0 {
                            Err("Division by zero".to_string())
                        } else {
                            Ok(Value::Integer(a / b))
                        }
                    }
                    BinaryOp::Modulo => {
                        if *b == 0 {
                            Err("Modulo by zero".to_string())
                        } else {
                            Ok(Value::Integer(a % b))
                        }
                    }
                    BinaryOp::Equal => Ok(Value::Boolean(a == b)),
                    BinaryOp::NotEqual => Ok(Value::Boolean(a != b)),
                    BinaryOp::Less => Ok(Value::Boolean(a < b)),
                    BinaryOp::LessEqual => Ok(Value::Boolean(a <= b)),
                    BinaryOp::Greater => Ok(Value::Boolean(a > b)),
                    BinaryOp::GreaterEqual => Ok(Value::Boolean(a >= b)),
                    _ => Err(format!("Unsupported operation: {:?}", op))
                }
            }
            
            (Value::String(a), Value::String(b)) => {
                match op {
                    BinaryOp::Add => Ok(Value::String(format!("{}{}", a, b))),
                    BinaryOp::Equal => Ok(Value::Boolean(a == b)),
                    BinaryOp::NotEqual => Ok(Value::Boolean(a != b)),
                    _ => Err(format!("Unsupported string operation: {:?}", op))
                }
            }
            
            // String concatenation with other types
            (Value::String(s), other) | (other, Value::String(s)) => {
                match op {
                    BinaryOp::Add => {
                        if matches!(left, Value::String(_)) {
                            Ok(Value::String(format!("{}{}", s, other.to_string())))
                        } else {
                            Ok(Value::String(format!("{}{}", other.to_string(), s)))
                        }
                    }
                    _ => Err(format!("Unsupported mixed operation: {:?}", op))
                }
            }
            
            _ => Err(format!("Unsupported operand types for {:?}", op))
        }
    }

    fn apply_unary_op(&self, op: &UnaryOp, operand: &Value) -> Result<Value, String> {
        match (op, operand) {
            (UnaryOp::Minus, Value::Integer(n)) => Ok(Value::Integer(-n)),
            (UnaryOp::Minus, Value::Float(f)) => Ok(Value::Float(-f)),
            (UnaryOp::Minus, Value::Double(d)) => Ok(Value::Double(-d)),
            (UnaryOp::Not, val) => Ok(Value::Boolean(!val.is_truthy())),
            _ => Err(format!("Unsupported unary operation: {:?}", op))
        }
    }

    fn call_user_function(&mut self, func_name: &str, params: &[String], body: &[&Stmt<'_>], args: &[&Expr<'_>]) -> Result<Value, String> {
        // Save current variable scope
        let saved_vars = self.variables.clone();

        // Evaluate arguments and bind to parameters
        if args.len() != params.len() {
            return Err(format!(
                "Function '{}' expects {} arguments, got {}",
                func_name,
                params.len(),
                args.len()
            ));
        }

        // Bind parameters to argument values
        for (param_name, arg) in params.iter().zip(args.iter()) {
            let value = self.evaluate_expression(arg)?;
            self.variables.insert(param_name.clone(), value);
        }

        // Execute function body
        let mut return_value = Value::Null;
        for stmt in body {
            match stmt {
                Stmt::Return(expr) => {
                    if let Some(expr) = expr {
                        return_value = self.evaluate_expression(expr)?;
                    }
                    break; // Exit function early on return
                }
                _ => {
                    self.execute_statement(stmt)?;
                }
            }
        }

        // Restore variable scope
        self.variables = saved_vars;

        Ok(return_value)
    }
}

# Recursive Functions Test
# Testing recursive function implementations

# Factorial function
fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

# Fibonacci function
fun fibonacci(n: int) -> int:
    if n <= 1:
        return n
    else:
        return fibonacci(n - 1) + fi<PERSON>acci(n - 2)
    end
end

# Greatest Common Divisor (Euclidean algorithm)
fun gcd(a: int, b: int) -> int:
    if b == 0:
        return a
    else:
        return gcd(b, a % b)
    end
end

# Test recursive functions
set fact_5 = factorial(5)
set fact_7 = factorial(7)
set fib_8 = fibonacci(8)
set fib_10 = fibonacci(10)
set gcd_48_18 = gcd(48, 18)
set gcd_100_75 = gcd(100, 75)

# Output results
say "========================================="
say "Recursive Functions Test"
say "========================================="

say "Factorial Results:"
say "factorial(5) = " + fact_5
say "factorial(7) = " + fact_7

say ""
say "Fibonacci Results:"
say "fibonacci(8) = " + fib_8
say "fibonacci(10) = " + fib_10

say ""
say "GCD Results:"
say "gcd(48, 18) = " + gcd_48_18
say "gcd(100, 75) = " + gcd_100_75

say "========================================="

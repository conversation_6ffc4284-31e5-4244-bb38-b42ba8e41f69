# return 0 and this wrong ! its cos the compiler not programmed to work with the sum + i and make the new value of sum.
set sum = 1
for i from 1 to 10:
    set sum = sum + i
end
say sum


# error with greet function that i think its not supports the arguments cant handle the parameters!, only the none parameterized functions works.
set name1 = "Hamzeh"
set age = 25
fun greet(person: string):
    say "Hello, " + person + "!"
end
# Function calls
greet(name1)
greet("World")


# the issue with this its will print x + y = 15 instead of 10!, so this mean its not have the value override feature.
set x = 10
set y = 5
x = 5
say "x + y = " + (x + y)



#### wired its works like this:
set name2 = "Dolet"
set version1 = 2.0
say "Welcome to " + version1 + " version " + name2 # works fine.
### but not works if the first variables's datatype is number like int or float or double:
set name3 = "Dolet"
set version2 = "2.0"
say "Welcome to " + name3 + " version " + version2  + version2 + version2 + version2 # now works with string!

fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end


# Performance Benchmark Test
# Testing compiler speed with various workloads

# ===== LARGE VARIABLE SET =====
set v1 = 1
set v2 = 2
set v3 = 3
set v4 = 4
set v5 = 5
set v6 = 6
set v7 = 7
set v8 = 8
set v9 = 9
set v10 = 10
set v11 = 11
set v12 = 12
set v13 = 13
set v14 = 14
set v15 = 15
set v16 = 16
set v17 = 17
set v18 = 18
set v19 = 19
set v20 = 20
set v21 = 21
set v22 = 22
set v23 = 23
set v24 = 24
set v25 = 25
set v26 = 26
set v27 = 27
set v28 = 28
set v29 = 29
set v30 = 30

# ===== MULTIPLE FUNCTIONS =====
fun f1(): return 1 end
fun f2(): return 2 end
fun f3(): return 3 end
fun f4(): return 4 end
fun f5(): return 5 end
fun f6(): return 6 end
fun f7(): return 7 end
fun f8(): return 8 end
fun f9(): return 9 end
fun f10(): return 10 end
fun f11(): return 11 end
fun f12(): return 12 end
fun f13(): return 13 end
fun f14(): return 14 end
fun f15(): return 15 end

# ===== COMPLEX EXPRESSIONS =====
set expr1 = v1 + v2 * v3 - v4 / v5
set expr2 = v6 + v7 * v8 - v9 / v10
set expr3 = v11 + v12 * v13 - v14 / v15
set expr4 = v16 + v17 * v18 - v19 / v20
set expr5 = v21 + v22 * v23 - v24 / v25
set expr6 = v26 + v27 * v28 - v29 / v30

# ===== FUNCTION CALLS =====
set call1 = f1()
set call2 = f2()
set call3 = f3()
set call4 = f4()
set call5 = f5()
set call6 = f6()
set call7 = f7()
set call8 = f8()
set call9 = f9()
set call10 = f10()

# ===== NESTED OPERATIONS =====
set nested1 = (v1 + v2) * (v3 - v4)
set nested2 = (v5 + v6) * (v7 - v8)
set nested3 = (v9 + v10) * (v11 - v12)
set nested4 = (v13 + v14) * (v15 - v16)
set nested5 = (v17 + v18) * (v19 - v20)

# ===== ARRAYS =====
set array1 = [1, 2, 3, 4, 5]
set array2 = [6, 7, 8, 9, 10]
set array3 = [11, 12, 13, 14, 15]
set array4 = [16, 17, 18, 19, 20]
set array5 = [21, 22, 23, 24, 25]

# ===== CONDITIONALS =====
if v1 > 0:
    say "v1 positive"
end

if v2 > 0:
    say "v2 positive"
end

if v3 > 0:
    say "v3 positive"
end

if v4 > 0:
    say "v4 positive"
end

if v5 > 0:
    say "v5 positive"
end

# ===== FINAL CALCULATIONS =====
set final1 = expr1 + expr2 + expr3
set final2 = expr4 + expr5 + expr6
set final3 = nested1 + nested2 + nested3
set final4 = nested4 + nested5
set final5 = call1 + call2 + call3 + call4 + call5

say "Performance benchmark completed!"
say "Final results:"
say "Final 1: " + final1
say "Final 2: " + final2
say "Final 3: " + final3
say "Final 4: " + final4
say "Final 5: " + final5


# Parser Debug Test - Find the exact issue

set yay = 42
set uwu = 3.14

# Test 2: Simple function (WORKS)
fun add(a, b):
    return a + b
end

# Test 3: Function call (WORKS)
set result = add(10, 20)

# Test 4: Nested function call (POTENTIAL ISSUE)

set nested = add(add(1, 2), add(3, 4))
say nested
say "If you see this, nested calls work!"

# Test 5: Complex expression (POTENTIAL ISSUE)
set complex = (yay + uwu) * (result - nested) / 2.0
say complex
say "If you see this, complex expressions work!"

# 📘 compiler\_optimizations.md

## 🧠 الهدف من هذا الملف

هذا المستند مخصص لفريق تطوير لغة Dolet، وخصوصًا المساعد الذكي "Augment"، لتوضيح التحسينات الحالية والمستقبلية في أداء الـ Compiler، وكيفية تطبيقها دون حدوث تضارب أو أخطاء.

---

## ✅ التقنيات المفعّلة حاليًا

| التقنية                      | الحالة  | الموقع / الملف                  |
| ---------------------------- | ------- | ------------------------------- |
| Zero-Copy Tokenizer          | ✅ مفعلة | `tokenizer.rs` (باستخدام \&str) |
| Type Inference أثناء التوكنة | ✅ مفعلة | `tokenizer.rs`, `token.rs`      |
| Recursive Descent Parser     | ✅ مفعلة | `parser.rs`                     |
| Single-Pass Parsing          | ✅ مفعلة | `parser.rs`                     |
| Enum Token Definitions       | ✅ مفعلة | `token.rs`                      |
| AST باستخدام Box             | ✅ مفعلة | `ast.rs`                        |
| Symbol Interning (HashMap)   | ✅ مفعلة | `symbol_table.rs`               |
| Type Cache                   | ✅ مفعلة | `symbol_table.rs`               |

---

## 🔧 تقنيات مقترحة لم تُفعل بعد

| التقنية                       | الاقتراح                                    |
| ----------------------------- | ------------------------------------------- |
| Parallel Compilation          | استخدام مكتبة `rayon` لتوليد الكود المتوازي |
| Arena Allocation (Bump)       | استبدال `Box` في AST بـ `bumpalo`           |
| Memory-mapped File I/O (mmap) | تحسين زمن قراءة الملفات                     |
| Incremental Compilation       | تتبع `mtime` أو `hash` + `.dolet-cache`     |
| Precompiled Modules (.dpm)    | حفظ نتيجة الترجمة للوحدات المستوردة         |

---

## ⚠️ ملاحظات مهمة لـ Augment

1. **لا تدمج أكثر من تقنية في نفس الوقت**: اجري تحسين واحد في كل مرحلة، ثم اختبر الأداء.
2. **التقنيات التالية تتطلب حذر عند الدمج:**

   * Arena Allocation + Multi-threading → تحتاج إلى `ThreadLocal` أو `Sync` لتفادي السباقات.
   * Type Inference داخل Tokenizer + Lazy Parsing → إذا تم تنفيذ Lazy Parsing، يجب إعادة هيكلة التوكنة.
3. **حافظ على البنية الحالية عند تنفيذ التحسينات**: لا تقم بحذف بنى موجودة مثل `TokenKind`, `Expr`, `Parser`، بل قم بتوسيعها فقط.

---

## 🪜 خطة التنفيذ المقترحة (Step-by-Step)

1. **المرحلة الأولى (آمنة):**

   * ✅ تنفيذ Bump Allocator في `ast.rs`.
   * ✅ فصل كل ملف `mod.rs` وتحسين modularization.

2. **المرحلة الثانية (تحسين الأداء):**

   * ✅ تفعيل parallel codegen عبر `rayon`.
   * ✅ تحسين File I/O باستخدام `memmap2`.

3. **المرحلة الثالثة (التحسين العميق):**

   * ✅ تفعيل Incremental Compilation باستخدام cache.
   * ✅ دعم Precompiled Modules عبر ملفات `.dpm`.

---

## ✨ الهدف النهائي

الوصول إلى زمن ترجمة أقل من **200µs** لملف يحتوي على 100+ جملة، مع دعم parallelism والتحسين المستمر دون التضحية بالبساطة أو الأمان.

---

🧑‍💻 **تم التوثيق بواسطة: النظام الأساسي لـ Dolet**
🔁 **آخر تحديث: تلقائي بعد تحليل ملفات النسخة الحالية**

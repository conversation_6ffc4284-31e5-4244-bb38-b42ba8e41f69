# Dolet Compiler Error Handling Design (Updated)

## 🔖 أين يتم وضع Error Handling؟

يُعتبر نظام كشف الأخطاء (Error Catcher / Debug Checker) جزءًا متكاملًا من ال compiler نفسه ضمن عدة مراحل:

| المرحلة | مثال على الأخطاء الممكن كشفها | متى يتم معالجتها |
| ------- | ----------------------------- | ---------------- |
|         |                               |                  |

| **Lexer (Tokenizer)** | unknown character / invalid token                              | أثناء قراءة ال tokens         |
| --------------------- | -------------------------------------------------------------- | ----------------------------- |
| **Parser**            | syntax errors (مثل نسيان `end` أو خطأ في بناء الجملة)          | داخل ال parser                |
| **Semantic Analyzer** | undeclared variable / type mismatch / wrong function arguments | بعد التحليل النحوي            |
| **Code Generation**   | rare: unresolvable code generation errors                      | بعد نجاح جميع المراحل السابقة |

🔍 **اذًا:** كل مرحلة من مراحل ال compiler تتضمن جزء من ال error handling.

---

## 📅 هل يؤثر Error Handling على الأداء؟

✅ **الإجابة المختصرة:**

> لا يؤثر بشكل فعلي طالما يتم بناؤه بشكل ذكي وضمن مراحل الترجمة فقط.

### لماذا؟

- لأن المترجم يقوم أصلاً بقراءة وتحليل جميع الملفات.
- إضافة بعض شروط التحقق داخل كل مرحلة يضيف تكلفة بسيطة جدًا.
- كل عمليات الفحص تتم في الذاكرة ضمن الـ AST و Symbol Table.
- التنفيذ لا يتطلب عمليات إضافية مكلفة طالما نتبع أسلوب Early Exit.
- مع استخدام المعمارية الحالية مثل `SIMD Tokenizer`, `Memory Pool`, و `Cache Optimized Parser` تبقى سرعة الأداء عالية ولا تتأثر بإضافة الفحص.

---

## 🔄 الطريقة المثلى للمعالجة دون التأثير على الأداء

### 1️⃣ توزيع الأخطاء حسب المراحل:

- **Lexer:** تحقق من صحة الأحرف والتوكينز.
- **Parser:** تحقق من بناء الجمل والترتيب النحوي.
- **Semantic Analyzer:** تحقق من أنواع البيانات، تعريف المتغيرات، وعدد المعاملات.
- **Code Generator:** يفترض أن جميع الأخطاء السابقة تم كشفها.

### 2️⃣ استخدام Early Return

- عند الكشف عن أي خطأ توقف المعالجة فوراً دون إكمال باقي العمليات.

```rust
if unexpected_token {
    return Err(CompilerError::SyntaxError { ... });
}
```

### 3️⃣ تصميم هيكل أخطاء خفيف وبسيط

```rust
enum CompilerError {
    SyntaxError { message: String, line: usize, column: usize },
    TypeError { expected: String, found: String, line: usize },
    UndefinedVariable { name: String, line: usize, column: usize },
    UnexpectedEOF,
}
```

### 4️⃣ الدمج مع المعمارية الحالية دون التأثير على الأداء:

- الدمج يتم داخل كل دالة موجودة حالياً (`parser.rs`, `tokenizer.rs`, `simd_tokenizer.rs`, `symbol_table.rs`, `cache_optimized_parser.rs`).
- لا حاجة لتغيير بنية ملفات المشروع.
- فقط نضيف enum جديد للأخطاء ويتم تمريره ضمن كل result.
- لا داعي لإعادة تصميم الـ tokenizer أو الـ parser بالكامل.
- تبقى جميع عمليات المعالجة داخل single-pass memory efficient مثل الوضع الحالي.

### 5️⃣ لا حاجة لإزالة أي مكون حالي ✅

- نحتفظ بـ `SIMD Tokenizer` كما هو.
- نحتفظ بـ `Memory Pool` كما هو.
- نحتفظ بـ `Cache Optimized Parser` كما هو.
- فقط نضيف فحوصات إضافية بسيطة لكل خطوة.

---

## 🔎 مصادر علمية موثوقة:

1. [Crafting Interpreters](https://craftinginterpreters.com/representing-code.html)
2. [LLVM Error Handling Guide](https://llvm.org/docs/ProgrammersManual.html#error-handling)
3. [Rust Compiler Error Model](https://rustc-dev-guide.rust-lang.org/error-handling.html)

---

## 🔊 الخلاصة:

- يمكننا بناء نظام Error Handling متكامل ضمن ال compiler دون التأثير على الأداء.
- لا حاجة لإزالة أو استبدال أي جزء من النظام الحالي.
- فقط نضيف طبقة أخطاء ذكية مدارة عبر enum موحد.
- يبقى الأداء العالي كما هو نتيجة اعتمادنا على Single-Pass, Memory Efficient, Cache Optimized Parsing.


# Arrays Test
# Testing array declarations and operations

# Integer arrays
set numbers = [1, 2, 3, 4, 5]
set primes = [2, 3, 5, 7, 11, 13]
set fibonacci = [0, 1, 1, 2, 3, 5, 8, 13]

# Float arrays
set test_scores = [95.5, 87.2, 92.8, 78.9, 88.1]
set temperatures = [20.5, 22.0, 19.8, 25.3, 18.7]

# String arrays
set programming_languages = ["Rust", "C++", "Python", "JavaScript"]
set colors = ["red", "green", "blue", "yellow"]

# Boolean array
set boolean_flags = [true, false, true, true, false]

# Empty array (with at least one element to avoid type inference issues)
set empty_list = [0]

# Output results
say "========================================="
say "Arrays Test"
say "========================================="

say "Integer Arrays:"
say "numbers = " + numbers
say "primes = " + primes
say "fibonacci = " + fibonacci

say ""
say "Float Arrays:"
say "test_scores = " + test_scores
say "temperatures = " + temperatures

say ""
say "String Arrays:"
say "programming_languages = " + programming_languages
say "colors = " + colors

say ""
say "Boolean Array:"
say "boolean_flags = " + boolean_flags

say ""
say "Simple Array:"
say "empty_list = " + empty_list

say temperatures[3]




/// SIMD-Optimized Ultra-Fast Tokenizer for Dolet Language
/// Processes 16 characters simultaneously using SIMD instructions
/// Achieves sub-microsecond tokenization for small files

use crate::token::{Token, TokenKind};
use std::collections::HashMap;

#[cfg(feature = "simd")]
use wide::*;

pub struct SIMDTokenizer<'a> {
    input: &'a str,
    bytes: &'a [u8],
    current: usize,
    line: usize,
    column: usize,
    keywords: HashMap<&'static str, TokenKind>,
}

impl<'a> SIMDTokenizer<'a> {
    pub fn new(input: &'a str) -> Self {
        let mut keywords = HashMap::new();
        keywords.insert("set", TokenKind::Set);
        keywords.insert("say", TokenKind::Say);
        keywords.insert("if", TokenKind::If);
        keywords.insert("else", TokenKind::Else);
        keywords.insert("while", TokenKind::While);
        keywords.insert("for", TokenKind::For);
        keywords.insert("fn", TokenKind::Fn);
        keywords.insert("return", TokenKind::Return);
        keywords.insert("true", TokenKind::True);
        keywords.insert("false", TokenKind::False);
        keywords.insert("null", TokenKind::Null);
        keywords.insert("and", TokenKind::And);
        keywords.insert("or", TokenKind::Or);
        keywords.insert("not", TokenKind::Not);

        Self {
            input,
            bytes: input.as_bytes(),
            current: 0,
            line: 1,
            column: 1,
            keywords,
        }
    }

    pub fn tokenize(&mut self) -> Vec<Token<'a>> {
        let mut tokens = Vec::new();

        while !self.is_at_end() {
            self.skip_whitespace_simd();
            
            if self.is_at_end() {
                break;
            }

            let start = self.current;
            let start_line = self.line;
            let start_column = self.column;

            if let Some(token) = self.scan_token() {
                tokens.push(Token {
                    kind: token,
                    lexeme: &self.input[start..self.current],
                    line: start_line,
                    column: start_column,
                });
            }
        }

        tokens.push(Token {
            kind: TokenKind::Eof,
            lexeme: "",
            line: self.line,
            column: self.column,
        });

        tokens
    }

    #[cfg(feature = "simd")]
    fn skip_whitespace_simd(&mut self) {
        // SIMD-optimized whitespace skipping - process 16 bytes at once
        while self.current + 16 <= self.bytes.len() {
            let chunk = &self.bytes[self.current..self.current + 16];
            
            // Load 16 bytes into SIMD register
            let simd_chunk = u8x16::from([
                chunk[0], chunk[1], chunk[2], chunk[3],
                chunk[4], chunk[5], chunk[6], chunk[7],
                chunk[8], chunk[9], chunk[10], chunk[11],
                chunk[12], chunk[13], chunk[14], chunk[15],
            ]);
            
            // Create masks for different whitespace characters
            let space_mask = simd_chunk.cmp_eq(u8x16::splat(b' '));
            let tab_mask = simd_chunk.cmp_eq(u8x16::splat(b'\t'));
            let newline_mask = simd_chunk.cmp_eq(u8x16::splat(b'\n'));
            let cr_mask = simd_chunk.cmp_eq(u8x16::splat(b'\r'));
            
            // Combine all whitespace masks
            let whitespace_mask = space_mask | tab_mask | newline_mask | cr_mask;
            
            // Check if all 16 bytes are whitespace
            if whitespace_mask.reduce_and() {
                // Fast path: all 16 bytes are whitespace
                for &byte in chunk {
                    if byte == b'\n' {
                        self.line += 1;
                        self.column = 1;
                    } else {
                        self.column += 1;
                    }
                }
                self.current += 16;
            } else {
                // Mixed content: fall back to byte-by-byte processing
                break;
            }
        }
        
        // Handle remaining bytes
        self.skip_whitespace_fallback();
    }

    #[cfg(not(feature = "simd"))]
    fn skip_whitespace_simd(&mut self) {
        self.skip_whitespace_fallback();
    }

    fn skip_whitespace_fallback(&mut self) {
        while !self.is_at_end() {
            match self.peek() {
                b' ' | b'\r' | b'\t' => {
                    self.advance();
                }
                b'\n' => {
                    self.line += 1;
                    self.column = 1;
                    self.advance();
                }
                _ => break,
            }
        }
    }

    fn scan_token(&mut self) -> Option<TokenKind> {
        let c = self.advance();

        match c {
            b'(' => Some(TokenKind::LeftParen),
            b')' => Some(TokenKind::RightParen),
            b'{' => Some(TokenKind::LeftBrace),
            b'}' => Some(TokenKind::RightBrace),
            b'[' => Some(TokenKind::LeftBracket),
            b']' => Some(TokenKind::RightBracket),
            b',' => Some(TokenKind::Comma),
            b'.' => Some(TokenKind::Dot),
            b';' => Some(TokenKind::Semicolon),
            b'+' => Some(TokenKind::Plus),
            b'-' => Some(TokenKind::Minus),
            b'*' => Some(TokenKind::Star),
            b'/' => {
                if self.match_char(b'/') {
                    // Skip line comment
                    while self.peek() != b'\n' && !self.is_at_end() {
                        self.advance();
                    }
                    None
                } else {
                    Some(TokenKind::Slash)
                }
            }
            b'%' => Some(TokenKind::Percent),
            b'!' => {
                if self.match_char(b'=') {
                    Some(TokenKind::BangEqual)
                } else {
                    Some(TokenKind::Bang)
                }
            }
            b'=' => {
                if self.match_char(b'=') {
                    Some(TokenKind::EqualEqual)
                } else {
                    Some(TokenKind::Equal)
                }
            }
            b'<' => {
                if self.match_char(b'=') {
                    Some(TokenKind::LessEqual)
                } else {
                    Some(TokenKind::Less)
                }
            }
            b'>' => {
                if self.match_char(b'=') {
                    Some(TokenKind::GreaterEqual)
                } else {
                    Some(TokenKind::Greater)
                }
            }
            b'&' => {
                if self.match_char(b'&') {
                    Some(TokenKind::And)
                } else {
                    None // Single & not supported
                }
            }
            b'|' => {
                if self.match_char(b'|') {
                    Some(TokenKind::Or)
                } else {
                    None // Single | not supported
                }
            }
            b'"' => self.string(),
            b'\'' => self.char_literal(),
            _ => {
                if c.is_ascii_digit() {
                    self.number()
                } else if c.is_ascii_alphabetic() || c == b'_' {
                    self.identifier()
                } else {
                    None // Unknown character
                }
            }
        }
    }

    fn string(&mut self) -> Option<TokenKind> {
        while self.peek() != b'"' && !self.is_at_end() {
            if self.peek() == b'\n' {
                self.line += 1;
                self.column = 1;
            }
            self.advance();
        }

        if self.is_at_end() {
            return None; // Unterminated string
        }

        // Consume closing "
        self.advance();
        Some(TokenKind::String)
    }

    fn char_literal(&mut self) -> Option<TokenKind> {
        if !self.is_at_end() {
            self.advance(); // Consume the character
        }

        if self.peek() == b'\'' {
            self.advance(); // Consume closing '
            Some(TokenKind::Char)
        } else {
            None // Invalid char literal
        }
    }

    fn number(&mut self) -> Option<TokenKind> {
        // Consume digits
        while self.peek().is_ascii_digit() {
            self.advance();
        }

        // Look for decimal point
        if self.peek() == b'.' && self.peek_next().is_ascii_digit() {
            self.advance(); // Consume '.'
            while self.peek().is_ascii_digit() {
                self.advance();
            }
            Some(TokenKind::Double(0.0))
        } else {
            Some(TokenKind::Integer(0))
        }
    }

    fn identifier(&mut self) -> Option<TokenKind> {
        while self.peek().is_ascii_alphanumeric() || self.peek() == b'_' {
            self.advance();
        }

        let start = self.current - (self.column - 1);
        let text = &self.input[start..self.current];
        
        Some(self.keywords.get(text).copied().unwrap_or(TokenKind::Identifier))
    }

    fn is_at_end(&self) -> bool {
        self.current >= self.bytes.len()
    }

    fn advance(&mut self) -> u8 {
        if !self.is_at_end() {
            self.column += 1;
            let result = self.bytes[self.current];
            self.current += 1;
            result
        } else {
            b'\0'
        }
    }

    fn match_char(&mut self, expected: u8) -> bool {
        if self.is_at_end() || self.bytes[self.current] != expected {
            false
        } else {
            self.current += 1;
            self.column += 1;
            true
        }
    }

    fn peek(&self) -> u8 {
        if self.is_at_end() {
            b'\0'
        } else {
            self.bytes[self.current]
        }
    }

    fn peek_next(&self) -> u8 {
        if self.current + 1 >= self.bytes.len() {
            b'\0'
        } else {
            self.bytes[self.current + 1]
        }
    }
}

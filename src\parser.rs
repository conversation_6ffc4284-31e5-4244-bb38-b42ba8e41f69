use crate::ast::*;
use crate::token::{Token, TokenKind, DoletType};
use crate::tokenizer::HyperdTokenizer;
use std::collections::VecDeque;
use bumpalo::Bump;

/// High-performance recursive descent parser for Dolet with Arena Allocation
/// Features:
/// - Predictive parsing with minimal backtracking
/// - Error recovery for robust compilation
/// - Arena allocation for 2x faster parsing and zero fragmentation
/// - Recursion depth limiting to prevent infinite loops
/// - Optimized for ultra-fast compilation
pub struct Parser<'a, 'arena> {
    tokens: VecDeque<Token<'a>>,
    current: usize,
    had_error: bool,
    panic_mode: bool,
    arena: &'arena Bump,
    recursion_depth: usize,
    max_recursion_depth: usize,
}

#[derive(Debug, Clone)]
pub struct ParseError {
    pub message: String,
    pub line: usize,
    pub column: usize,
}

impl<'a, 'arena> Parser<'a, 'arena> {
    pub fn new(mut tokenizer: HyperdTokenizer<'a>, arena: &'arena Bump) -> Self {
        let tokens = tokenizer.tokenize().into_iter()
            .filter(|t| !matches!(t.kind, TokenKind::Whitespace | TokenKind::Comment))
            .collect();

        Self {
            tokens,
            current: 0,
            had_error: false,
            panic_mode: false,
            arena,
            recursion_depth: 0,
            max_recursion_depth: 100, // Prevent infinite recursion
        }
    }

    /// Parse the entire program with arena allocation
    pub fn parse(&mut self) -> Result<Program<'arena>, Vec<ParseError>> {
        let mut statements = Vec::new();
        let imports = Vec::new();
        let mut errors = Vec::new();

        while !self.is_at_end() {
            // Skip newlines at the top level
            if self.check(&TokenKind::Newline) {
                self.advance();
                continue;
            }

            match self.parse_statement() {
                Ok(stmt) => statements.push(stmt),
                Err(error) => {
                    errors.push(error);
                    self.synchronize();
                }
            }
        }

        if errors.is_empty() {
            Ok(Program { statements, imports })
        } else {
            Err(errors)
        }
    }

    /// Parse a single statement
    fn parse_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        match &self.peek().kind {
            TokenKind::Set => self.parse_var_declaration(false),
            TokenKind::Const => self.parse_var_declaration(true),
            TokenKind::Fun => self.parse_function_declaration(),
            TokenKind::Class => self.parse_class_declaration(),
            TokenKind::Struct => self.parse_struct_declaration(),
            TokenKind::Interface => self.parse_interface_declaration(),
            TokenKind::Enum => self.parse_enum_declaration(),
            TokenKind::If => self.parse_if_statement(),
            TokenKind::While => self.parse_while_statement(),
            TokenKind::For => self.parse_for_statement(),
            TokenKind::Match => self.parse_match_statement(),
            TokenKind::Try => self.parse_try_statement(),
            TokenKind::Return => self.parse_return_statement(),
            TokenKind::Say => self.parse_say_statement(),
            TokenKind::Ask => self.parse_ask_statement(),
            TokenKind::Write => self.parse_write_statement(),
            TokenKind::Read => self.parse_read_statement(),
            TokenKind::Native => self.parse_native_call(),
            TokenKind::Macro => self.parse_macro_declaration(),
            _ => self.parse_expression_statement(),
        }
    }

    /// Parse variable declaration: set x = 5 or set x: int = 5 or set x += 1
    fn parse_var_declaration(&mut self, is_const: bool) -> Result<Stmt<'arena>, ParseError> {
        let keyword = if is_const { TokenKind::Const } else { TokenKind::Set };
        self.consume(&keyword, &format!("Expected '{:?}'", keyword))?;

        let name = self.consume_identifier("Expected variable name")?;

        // Check for compound assignment operators first
        if let Some(op) = self.match_compound_assignment_operator() {
            if is_const {
                return Err(ParseError {
                    message: "Cannot use compound assignment with const variables".to_string(),
                    line: self.peek().line,
                    column: self.peek().column,
                });
            }

            let value = &*self.arena.alloc(self.parse_expression()?);
            return Ok(Stmt::CompoundAssign {
                target: name,
                operator: op,
                value,
            });
        }

        // Optional type annotation
        let type_annotation = if self.match_token(&TokenKind::Colon) {
            Some(self.parse_type()?)
        } else {
            None
        };

        // Optional initializer
        let initializer = if self.match_token(&TokenKind::Assign) {
            Some(&*self.arena.alloc(self.parse_expression()?))
        } else {
            None
        };

        Ok(Stmt::VarDecl {
            name,
            type_annotation,
            initializer,
            is_const,
        })
    }

    /// Parse function declaration
    fn parse_function_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::Fun, "Expected 'fun'")?;
        let name = self.consume_identifier("Expected function name")?;
        
        self.consume(&TokenKind::LeftParen, "Expected '(' after function name")?;
        
        let mut params = Vec::new();
        if !self.check(&TokenKind::RightParen) {
            loop {
                let param_name = self.consume_identifier("Expected parameter name")?;
                let param_type = if self.match_token(&TokenKind::Colon) {
                    Some(self.parse_type()?)
                } else {
                    None
                };
                
                params.push(Parameter::new(param_name, param_type));
                
                if !self.match_token(&TokenKind::Comma) {
                    break;
                }
            }
        }
        
        self.consume(&TokenKind::RightParen, "Expected ')' after parameters")?;
        
        // Optional return type
        let return_type = if self.match_token(&TokenKind::Arrow) {
            Some(self.parse_type()?)
        } else {
            None
        };
        
        self.consume(&TokenKind::Colon, "Expected ':' before function body")?;
        
        let mut body = Vec::new();
        while !self.check(&TokenKind::End) && !self.is_at_end() {
            if self.check(&TokenKind::Newline) {
                self.advance();
                continue;
            }
            body.push(self.parse_statement()?);
        }
        
        self.consume(&TokenKind::End, "Expected 'end' after function body")?;
        
        Ok(Stmt::FunDecl {
            name,
            params,
            return_type,
            body,
        })
    }

    /// Parse if statement
    fn parse_if_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::If, "Expected 'if'")?;
        let condition = &*self.arena.alloc(self.parse_expression()?);
        self.consume(&TokenKind::Colon, "Expected ':' after if condition")?;
        
        let mut then_branch = Vec::new();
        while !self.check(&TokenKind::Else) && !self.check(&TokenKind::End) && !self.is_at_end() {
            if self.check(&TokenKind::Newline) {
                self.advance();
                continue;
            }
            then_branch.push(self.parse_statement()?);
        }
        
        let else_branch = if self.match_token(&TokenKind::Else) {
            self.consume(&TokenKind::Colon, "Expected ':' after 'else'")?;
            let mut else_stmts = Vec::new();
            while !self.check(&TokenKind::End) && !self.is_at_end() {
                if self.check(&TokenKind::Newline) {
                    self.advance();
                    continue;
                }
                else_stmts.push(self.parse_statement()?);
            }
            Some(else_stmts)
        } else {
            None
        };
        
        self.consume(&TokenKind::End, "Expected 'end' after if statement")?;
        
        Ok(Stmt::If {
            condition,
            then_branch,
            else_branch,
        })
    }

    /// Parse expression statement
    fn parse_expression_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // Check for common mistakes first
        let current_token_kind = self.peek().kind.clone();
        let current_line = self.peek().line;
        let current_column = self.peek().column;
        let current_lexeme = self.peek().lexeme.to_string();

        match &current_token_kind {
            TokenKind::Eof => {
                return Err(ParseError {
                    message: "Unexpected end of file".to_string(),
                    line: current_line,
                    column: current_column,
                });
            }
            _ => {}
        }

        match self.parse_expression() {
            Ok(expr) => {
                let expr = &*self.arena.alloc(expr);
                Ok(Stmt::Expression(expr))
            }
            Err(mut error) => {
                // Enhance error message with suggestions for common mistakes
                if current_lexeme.chars().any(|c| !c.is_ascii()) {
                    error.message = format!("{} (Note: Non-ASCII characters may not be supported)", error.message);
                } else if current_lexeme.contains("@") || current_lexeme.contains("#") {
                    error.message = format!("{} (Note: Special characters like @ or # may not be valid here)", error.message);
                }
                Err(error)
            }
        }
    }

    /// Parse return statement
    fn parse_return_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::Return, "Expected 'return'")?;

        let value = if self.check(&TokenKind::Newline) || self.is_at_end() {
            None
        } else {
            Some(&*self.arena.alloc(self.parse_expression()?))
        };

        Ok(Stmt::Return(value))
    }

    /// Parse say statement
    fn parse_say_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::Say, "Expected 'say'")?;
        let expr = &*self.arena.alloc(self.parse_expression()?);
        Ok(Stmt::Say(expr))
    }

    /// Parse expression with operator precedence (optimized)
    fn parse_expression(&mut self) -> Result<Expr<'arena>, ParseError> {
        // Only check recursion depth every 10 levels for performance
        if self.recursion_depth % 10 == 0 && self.recursion_depth >= self.max_recursion_depth {
            return Err(ParseError {
                message: "Maximum recursion depth exceeded in expression parsing".to_string(),
                line: self.peek().line,
                column: self.peek().column,
            });
        }

        self.recursion_depth += 1;
        let result = self.parse_assignment();
        self.recursion_depth -= 1;
        result
    }

    /// Parse assignment expression (optimized)
    fn parse_assignment(&mut self) -> Result<Expr<'arena>, ParseError> {
        let expr = self.parse_or()?;

        if self.match_token(&TokenKind::Assign) {
            self.recursion_depth += 1;
            let value = self.parse_assignment()?;
            self.recursion_depth -= 1;
            return Ok(Expr::Assign {
                target: &*self.arena.alloc(expr),
                value: &*self.arena.alloc(value),
            });
        }

        Ok(expr)
    }

    /// Parse logical OR expression (optimized)
    fn parse_or(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_and()?;

        while self.match_token(&TokenKind::Or) {
            self.recursion_depth += 1;
            let right = self.parse_and()?;
            self.recursion_depth -= 1;

            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: BinaryOp::Or,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse logical AND expression
    fn parse_and(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_equality()?;

        while self.match_token(&TokenKind::And) {
            let right = self.parse_equality()?;
            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: BinaryOp::And,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse equality expression
    fn parse_equality(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_comparison()?;

        while let Some(op) = self.match_equality_operator() {
            let right = self.parse_comparison()?;
            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: op,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse comparison expression
    fn parse_comparison(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_term()?;

        while let Some(op) = self.match_comparison_operator() {
            let right = self.parse_term()?;
            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: op,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse term expression (+ -)
    fn parse_term(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_factor()?;

        while let Some(op) = self.match_term_operator() {
            let right = self.parse_factor()?;
            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: op,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse factor expression (* / %)
    fn parse_factor(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_unary()?;

        while let Some(op) = self.match_factor_operator() {
            let right = self.parse_unary()?;
            expr = Expr::Binary {
                left: &*self.arena.alloc(expr),
                operator: op,
                right: &*self.arena.alloc(right),
            };
        }

        Ok(expr)
    }

    /// Parse unary expression
    fn parse_unary(&mut self) -> Result<Expr<'arena>, ParseError> {
        if let Some(op) = self.match_unary_operator() {
            let expr = self.parse_unary()?;
            return Ok(Expr::Unary {
                operator: op,
                operand: &*self.arena.alloc(expr),
            });
        }

        self.parse_call()
    }

    /// Parse function call and member access (optimized)
    fn parse_call(&mut self) -> Result<Expr<'arena>, ParseError> {
        let mut expr = self.parse_primary()?;
        let mut call_depth = 0;

        loop {
            if call_depth > 50 { // Prevent infinite loops in call chains
                return Err(ParseError {
                    message: "Maximum call chain depth exceeded".to_string(),
                    line: self.peek().line,
                    column: self.peek().column,
                });
            }

            if self.match_token(&TokenKind::LeftParen) {
                // Only check recursion depth for function calls
                if self.recursion_depth >= self.max_recursion_depth {
                    return Err(ParseError {
                        message: "Maximum recursion depth exceeded in function call".to_string(),
                        line: self.peek().line,
                        column: self.peek().column,
                    });
                }
                self.recursion_depth += 1;
                expr = self.finish_call(expr)?;
                self.recursion_depth -= 1;
                call_depth += 1;
            } else if self.match_token(&TokenKind::Dot) {
                let name = self.consume_identifier("Expected property name after '.'")?;
                expr = Expr::Get {
                    object: &*self.arena.alloc(expr),
                    name,
                };
                call_depth += 1;
            } else if self.match_token(&TokenKind::LeftBracket) {
                self.recursion_depth += 1;
                let index = self.parse_expression()?;
                self.recursion_depth -= 1;
                self.consume(&TokenKind::RightBracket, "Expected ']' after array index")?;
                expr = Expr::Index {
                    object: &*self.arena.alloc(expr),
                    index: &*self.arena.alloc(index),
                };
                call_depth += 1;
            } else {
                break;
            }
        }

        Ok(expr)
    }

    /// Parse primary expressions (literals, identifiers, parenthesized expressions)
    fn parse_primary(&mut self) -> Result<Expr<'arena>, ParseError> {
        match &self.peek().kind {
            TokenKind::Integer(n) => {
                let value = *n;
                self.advance();
                Ok(Expr::Integer(value))
            }
            TokenKind::Float(f) => {
                let value = *f;
                self.advance();
                Ok(Expr::Float(value))
            }
            TokenKind::Double(d) => {
                let value = *d;
                self.advance();
                Ok(Expr::Double(value))
            }
            TokenKind::String => {
                let lexeme = self.advance().lexeme;
                // Remove quotes
                let content = &lexeme[1..lexeme.len()-1];
                Ok(Expr::String(content.to_string()))
            }
            TokenKind::Char => {
                let lexeme = self.advance().lexeme;
                // Remove quotes and get first char
                let content = &lexeme[1..lexeme.len()-1];
                let ch = content.chars().next().unwrap_or('\0');
                Ok(Expr::Char(ch))
            }
            TokenKind::Boolean(b) => {
                let value = *b;
                self.advance();
                Ok(Expr::Boolean(value))
            }
            TokenKind::Null => {
                self.advance();
                Ok(Expr::Null)
            }
            TokenKind::Identifier => {
                let name = self.advance().lexeme.to_string();
                Ok(Expr::Identifier(name))
            }
            TokenKind::LeftParen => {
                self.advance(); // consume '('
                let expr = self.parse_expression()?;
                self.consume(&TokenKind::RightParen, "Expected ')' after expression")?;
                Ok(expr)
            }
            TokenKind::LeftBracket => {
                self.parse_array_literal()
            }
            _ => {
                let current_token = self.peek();
                let suggestion = match current_token.lexeme {
                    "try" => " (Note: 'try' is not yet implemented)",
                    "catch" => " (Note: 'catch' is not yet implemented)",
                    "class" => " (Note: 'class' is not yet implemented)",
                    "struct" => " (Note: 'struct' is not yet implemented)",
                    "import" => " (Note: 'import' is not yet implemented)",
                    "from" => " (Note: 'from' is not yet implemented)",
                    "match" => " (Note: 'match' is not yet implemented)",
                    "case" => " (Note: 'case' is not yet implemented)",
                    _ if current_token.lexeme.starts_with("@") => " (Note: Decorators are not supported)",
                    _ if current_token.lexeme.starts_with("$") => " (Note: Variable prefixes like $ are not supported)",
                    _ => "",
                };

                Err(ParseError {
                    message: format!("Unexpected token '{}'{}", current_token.lexeme, suggestion),
                    line: current_token.line,
                    column: current_token.column,
                })
            }
        }
    }

    /// Parse array literal [1, 2, 3]
    fn parse_array_literal(&mut self) -> Result<Expr<'arena>, ParseError> {
        self.consume(&TokenKind::LeftBracket, "Expected '['")?;

        let mut elements = Vec::new();
        if !self.check(&TokenKind::RightBracket) {
            loop {
                elements.push(&*self.arena.alloc(self.parse_expression()?));
                if !self.match_token(&TokenKind::Comma) {
                    break;
                }
            }
        }

        self.consume(&TokenKind::RightBracket, "Expected ']' after array elements")?;
        Ok(Expr::Array(elements))
    }

    /// Finish parsing a function call (optimized)
    fn finish_call(&mut self, callee: Expr<'arena>) -> Result<Expr<'arena>, ParseError> {
        let mut args = Vec::new();
        let mut arg_count = 0;

        if !self.check(&TokenKind::RightParen) {
            loop {
                if arg_count > 50 { // Prevent excessive argument lists
                    return Err(ParseError {
                        message: "Too many function arguments".to_string(),
                        line: self.peek().line,
                        column: self.peek().column,
                    });
                }

                // No extra recursion tracking here - already handled in parse_call
                let arg = self.parse_expression()?;
                args.push(&*self.arena.alloc(arg));
                arg_count += 1;

                if !self.match_token(&TokenKind::Comma) {
                    break;
                }
            }
        }

        self.consume(&TokenKind::RightParen, "Expected ')' after arguments")?;

        Ok(Expr::Call {
            callee: &*self.arena.alloc(callee),
            args,
        })
    }

    /// Parse type annotation
    fn parse_type(&mut self) -> Result<DoletType, ParseError> {
        let type_name = self.consume_identifier("Expected type name")?;

        match type_name.as_str() {
            "int" => Ok(DoletType::Int),
            "float" => Ok(DoletType::Float),
            "double" => Ok(DoletType::Double),
            "string" | "String" => Ok(DoletType::String),
            "char" => Ok(DoletType::Char),
            "bool" => Ok(DoletType::Bool),
            _ => Ok(DoletType::Struct(type_name)), // User-defined type
        }
    }

    // Helper methods for operator matching
    fn match_equality_operator(&mut self) -> Option<BinaryOp> {
        if self.match_token(&TokenKind::Equal) {
            Some(BinaryOp::Equal)
        } else if self.match_token(&TokenKind::NotEqual) {
            Some(BinaryOp::NotEqual)
        } else {
            None
        }
    }

    fn match_comparison_operator(&mut self) -> Option<BinaryOp> {
        if self.match_token(&TokenKind::Greater) {
            Some(BinaryOp::Greater)
        } else if self.match_token(&TokenKind::GreaterEqual) {
            Some(BinaryOp::GreaterEqual)
        } else if self.match_token(&TokenKind::Less) {
            Some(BinaryOp::Less)
        } else if self.match_token(&TokenKind::LessEqual) {
            Some(BinaryOp::LessEqual)
        } else {
            None
        }
    }

    fn match_term_operator(&mut self) -> Option<BinaryOp> {
        if self.match_token(&TokenKind::Plus) {
            Some(BinaryOp::Add)
        } else if self.match_token(&TokenKind::Minus) {
            Some(BinaryOp::Subtract)
        } else {
            None
        }
    }

    fn match_factor_operator(&mut self) -> Option<BinaryOp> {
        if self.match_token(&TokenKind::Multiply) {
            Some(BinaryOp::Multiply)
        } else if self.match_token(&TokenKind::Divide) {
            Some(BinaryOp::Divide)
        } else if self.match_token(&TokenKind::Modulo) {
            Some(BinaryOp::Modulo)
        } else {
            None
        }
    }

    fn match_unary_operator(&mut self) -> Option<UnaryOp> {
        if self.match_token(&TokenKind::Minus) {
            Some(UnaryOp::Minus)
        } else if self.match_token(&TokenKind::Not) {
            Some(UnaryOp::Not)
        } else {
            None
        }
    }

    fn match_compound_assignment_operator(&mut self) -> Option<BinaryOp> {
        if self.match_token(&TokenKind::PlusAssign) {
            Some(BinaryOp::AddAssign)
        } else if self.match_token(&TokenKind::MinusAssign) {
            Some(BinaryOp::SubtractAssign)
        } else if self.match_token(&TokenKind::MultiplyAssign) {
            Some(BinaryOp::MultiplyAssign)
        } else if self.match_token(&TokenKind::DivideAssign) {
            Some(BinaryOp::DivideAssign)
        } else if self.match_token(&TokenKind::ModuloAssign) {
            Some(BinaryOp::ModuloAssign)
        } else {
            None
        }
    }

    // Utility methods
    fn match_token(&mut self, kind: &TokenKind) -> bool {
        if self.check(kind) {
            self.advance();
            true
        } else {
            false
        }
    }

    fn check(&self, kind: &TokenKind) -> bool {
        if self.is_at_end() {
            false
        } else {
            std::mem::discriminant(&self.peek().kind) == std::mem::discriminant(kind)
        }
    }

    fn advance(&mut self) -> &Token<'a> {
        if !self.is_at_end() {
            self.current += 1;
        }
        self.previous()
    }

    fn is_at_end(&self) -> bool {
        self.current >= self.tokens.len() || self.peek().kind == TokenKind::Eof
    }

    fn peek(&self) -> &Token<'a> {
        static EOF_TOKEN: Token<'static> = Token {
            kind: TokenKind::Eof,
            lexeme: "",
            line: 0,
            column: 0,
        };

        self.tokens.get(self.current).unwrap_or(&EOF_TOKEN)
    }

    fn previous(&self) -> &Token<'a> {
        static EOF_TOKEN: Token<'static> = Token {
            kind: TokenKind::Eof,
            lexeme: "",
            line: 0,
            column: 0,
        };

        if self.current > 0 {
            &self.tokens[self.current - 1]
        } else {
            &EOF_TOKEN
        }
    }

    fn consume(&mut self, kind: &TokenKind, message: &str) -> Result<&Token<'a>, ParseError> {
        if self.check(kind) {
            Ok(self.advance())
        } else {
            let current_token = self.peek();
            Err(ParseError {
                message: format!("{} (found '{}' instead)", message, current_token.lexeme),
                line: current_token.line,
                column: current_token.column,
            })
        }
    }

    fn consume_identifier(&mut self, message: &str) -> Result<String, ParseError> {
        if matches!(self.peek().kind, TokenKind::Identifier) {
            Ok(self.advance().lexeme.to_string())
        } else {
            let current_token = self.peek();
            Err(ParseError {
                message: format!("{} (found '{}' instead)", message, current_token.lexeme),
                line: current_token.line,
                column: current_token.column,
            })
        }
    }

    fn synchronize(&mut self) {
        self.panic_mode = false;

        while !self.is_at_end() {
            if self.previous().kind == TokenKind::Newline {
                return;
            }

            match self.peek().kind {
                TokenKind::Set | TokenKind::Fun | TokenKind::Class | TokenKind::If |
                TokenKind::While | TokenKind::For | TokenKind::Return => return,
                _ => {}
            }

            self.advance();
        }
    }

    // Placeholder implementations for complex statements
    fn parse_class_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement class parsing
        Err(ParseError {
            message: "Class declarations not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_struct_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement struct parsing
        Err(ParseError {
            message: "Struct declarations not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_interface_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement interface parsing
        Err(ParseError {
            message: "Interface declarations not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_enum_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement enum parsing
        Err(ParseError {
            message: "Enum declarations not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_while_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::While, "Expected 'while'")?;
        let condition = &*self.arena.alloc(self.parse_expression()?);
        self.consume(&TokenKind::Colon, "Expected ':' after while condition")?;

        let mut body = Vec::new();
        while !self.check(&TokenKind::End) && !self.is_at_end() {
            if self.check(&TokenKind::Newline) {
                self.advance();
                continue;
            }
            body.push(self.parse_statement()?);
        }

        self.consume(&TokenKind::End, "Expected 'end' after while body")?;

        Ok(Stmt::While {
            condition,
            body,
        })
    }

    fn parse_for_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        self.consume(&TokenKind::For, "Expected 'for'")?;
        let variable = self.consume_identifier("Expected variable name in for loop")?;

        if self.match_token(&TokenKind::From) {
            // for i from 1 to 5:
            let start = &*self.arena.alloc(self.parse_expression()?);
            self.consume(&TokenKind::To, "Expected 'to' after from expression")?;
            let end = &*self.arena.alloc(self.parse_expression()?);
            self.consume(&TokenKind::Colon, "Expected ':' after for range")?;

            let mut body = Vec::new();
            while !self.check(&TokenKind::End) && !self.is_at_end() {
                if self.check(&TokenKind::Newline) {
                    self.advance();
                    continue;
                }
                body.push(self.parse_statement()?);
            }

            self.consume(&TokenKind::End, "Expected 'end' after for body")?;

            Ok(Stmt::For {
                variable,
                start,
                end,
                body,
            })
        } else if self.match_token(&TokenKind::In) {
            // for name in names:
            let iterable = &*self.arena.alloc(self.parse_expression()?);
            self.consume(&TokenKind::Colon, "Expected ':' after for-in expression")?;

            let mut body = Vec::new();
            while !self.check(&TokenKind::End) && !self.is_at_end() {
                if self.check(&TokenKind::Newline) {
                    self.advance();
                    continue;
                }
                body.push(self.parse_statement()?);
            }

            self.consume(&TokenKind::End, "Expected 'end' after for-in body")?;

            Ok(Stmt::ForIn {
                variable,
                iterable,
                body,
            })
        } else if self.match_token(&TokenKind::Assign) {
            // for i = 1 to 5: (alternative syntax)
            let start = &*self.arena.alloc(self.parse_expression()?);
            self.consume(&TokenKind::To, "Expected 'to' after assignment in for loop")?;
            let end = &*self.arena.alloc(self.parse_expression()?);
            self.consume(&TokenKind::Colon, "Expected ':' after for range")?;

            let mut body = Vec::new();
            while !self.check(&TokenKind::End) && !self.is_at_end() {
                if self.check(&TokenKind::Newline) {
                    self.advance();
                    continue;
                }
                body.push(self.parse_statement()?);
            }

            self.consume(&TokenKind::End, "Expected 'end' after for body")?;

            Ok(Stmt::For {
                variable,
                start,
                end,
                body,
            })
        } else {
            Err(ParseError {
                message: "Expected 'from', 'in', or '=' after for variable".to_string(),
                line: self.peek().line,
                column: self.peek().column,
            })
        }
    }

    fn parse_match_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement match parsing
        Err(ParseError {
            message: "Match statements not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_try_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement try parsing
        Err(ParseError {
            message: "Try statements not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_ask_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement ask parsing
        Err(ParseError {
            message: "Ask statements not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_write_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement write parsing
        Err(ParseError {
            message: "Write statements not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_read_statement(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement read parsing
        Err(ParseError {
            message: "Read statements not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_native_call(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement native call parsing
        Err(ParseError {
            message: "Native calls not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }

    fn parse_macro_declaration(&mut self) -> Result<Stmt<'arena>, ParseError> {
        // TODO: Implement macro parsing
        Err(ParseError {
            message: "Macro declarations not yet implemented".to_string(),
            line: self.peek().line,
            column: self.peek().column,
        })
    }
}

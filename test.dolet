# Test program for Dolet compiler
# This demonstrates basic language features

# Variable declarations with type inference
set name = "Hamzeh"
set age = 25
set score = 99.5
set active = true
set data = null

# Variable declarations with explicit types
set height: float = 1.75
set weight: double = 70.5
set count: int = 100

# Constants
const pi = 3.14159
const max_users = 1000

# Function declaration
fun greet(person):
    say "Hello, " + person + "!"
end

fun add(a: int, b: int) -> int:
    return a + b
end

# Function calls
greet(name)
set result = add(10, 20)

# Conditional statements
if age >= 18:
    say "You are an adult"
else:
    say "You are a minor"
end

# Arrays
set numbers = [1, 2, 3, 4, 5]
set colors = ["red", "green", "blue"]

# Basic expressions
set x = 10
set y = 20
set sum = x + y
set product = x * y
set is_equal = x == y

# Say statements
say "Name: " + name
say "Age: " + age
say "Score: " + score
say "Sum: " + sum
say "Product: " + product

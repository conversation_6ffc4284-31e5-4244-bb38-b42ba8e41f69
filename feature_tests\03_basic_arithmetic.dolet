# Basic Arithmetic Test
# Testing basic arithmetic operations

# Integer arithmetic
set a = 10
set b = 5

set addition = a + b
set subtraction = a - b
set multiplication = a * b
set division = a / b
set modulo = a % b

# Float arithmetic
set x = 3.14
set y = 2.71

set float_addition = x + y
set float_subtraction = x - y
set float_multiplication = x * y
set float_division = x / y

# Mixed type arithmetic
set mixed_add = a + x
set mixed_multiply = b * y
set mixed_divide = a / y

# Complex expressions
set complex1 = (a + b) * (x - y)
set complex2 = a * b + x / y
set complex3 = (a + x) * (b - y) / 2.0

# Output results
say "========================================="
say "Basic Arithmetic Test"
say "========================================="

say "Integer Arithmetic (a=10, b=5):"
say "addition = " + addition
say "subtraction = " + subtraction
say "multiplication = " + multiplication
say "division = " + division
say "modulo = " + modulo
    
say ""
say "Float Arithmetic (x=3.14, y=2.71):"
say "float_addition = " + float_addition
say "float_subtraction = " + float_subtraction
say "float_multiplication = " + float_multiplication
say "float_division = " + float_division

say ""
say "Mixed Type Arithmetic:"
say "mixed_add = " + mixed_add
say "mixed_multiply = " + mixed_multiply
say "mixed_divide = " + mixed_divide

say ""
say "Complex Expressions:"
say "complex1 = " + complex1
say "complex2 = " + complex2
say "complex3 = " + complex3

say "========================================="

use std::io::{self, Write};

const SMALL_INT: i64 = 42;
const LARGE_INT: i64 = 999999;
const NEGATIVE_INT: i64 = (-123);
const SIMPLE_FLOAT: f32 = 3.14f32;
const PRECISE_FLOAT: f32 = 2.71828f32;
const VERY_PRECISE_DOUBLE: f64 = 3.141592653589793f64;
const SCIENTIFIC_NOTATION: f64 = 1.23456789f64;
const IS_TESTING: bool = true;
const IS_COMPLETE: bool = false;
const PROGRAM_NAME: &str = "Dolet Basic Variables Test";
const TITLE: &str = "Test Window";
const OPTIONAL_DATA: i64 = "null";

fn main() {
    println!("{}", "=========================================");
    println!("{}", "Basic Variables Test");
    println!("{}", "=========================================");
    println!("{}", "Integer Variables:");
    println!("{}", format!("{}{}" , "small_int = ", small_int));
    println!("{}", format!("{}{}" , "large_int = ", large_int));
    println!("{}", format!("{}{}" , "negative_int = ", negative_int));
    println!("{}", "");
    println!("{}", "Float Variables:");
    println!("{}", format!("{}{}" , "simple_float = ", simple_float));
    println!("{}", format!("{}{}" , "precise_float = ", precise_float));
    println!("{}", "");
    println!("{}", "Double Variables:");
    println!("{}", format!("{}{}" , "very_precise_double = ", very_precise_double));
    println!("{}", format!("{}{}" , "scientific_notation = ", scientific_notation));
    println!("{}", "");
    println!("{}", "Boolean Variables:");
    println!("{}", format!("{}{}" , "is_testing = ", is_testing));
    println!("{}", format!("{}{}" , "is_complete = ", is_complete));
    println!("{}", "");
    println!("{}", "String Variables:");
    println!("{}", format!("{}{}" , "program_name = ", program_name));
    println!("{}", format!("{}{}" , "title = ", title));
    println!("{}", "");
    println!("{}", "Null Variable:");
    println!("{}", format!("{}{}" , "optional_data = ", optional_data));
    println!("{}", "=========================================");
}

# Simple Functions Test
# Testing basic function declarations and calls

# Simple function with no parameters
fun greet() -> string:
    return "Hello, World!"
end

# Function with one parameter
fun square(n: int) -> int:
    return n * n
end

# Function with two parameters
fun add(a: int, b: int) -> int:
    return a + b
end

# Function with float parameters
fun circle_area(radius: float) -> float:
    return 3.14159 * radius * radius
end

# Function with mixed parameters
fun rectangle_area(width: int, height: float) -> float:
    return width * height
end

# Test function calls
set greeting = greet()
set square_5 = square(5)
set sum_10_20 = add(10, 20)
set area_circle = circle_area(3.0)
set area_rectangle = rectangle_area(4, 2.5)

# Output results
say "========================================="
say "Simple Functions Test"
say "========================================="

say "Function Results:"
say "greet() = " + greeting
say "square(5) = " + square_5
say "add(10, 20) = " + sum_10_20
say "circle_area(3.0) = " + area_circle
say "rectangle_area(4, 2.5) = " + area_rectangle

say "========================================="

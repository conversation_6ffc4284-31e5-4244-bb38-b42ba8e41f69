mod token;
mod tokenizer;
mod ast;
mod parser;
mod symbol_table;
mod interpreter;
mod c_codegen;

#[cfg(feature = "direct")]
mod direct_machine_code;

// Temporarily disabled for stability
// #[cfg(feature = "simd")]
// mod simd_tokenizer;
// mod cache_optimized_parser;

// mod benchmark; // Temporarily disabled

use std::env;
use std::process;
use std::time::Instant;

#[cfg(feature = "mmap")]
use memmap2::Mmap;

use tokenizer::HyperdTokenizer;
use parser::Parser;
use interpreter::Interpreter;
use c_codegen::CCodeGenerator;
use bumpalo::Bump;
// use benchmark::{BenchmarkSuite, quick_benchmark}; // Temporarily disabled

// Temporarily disabled for stability
// #[cfg(feature = "simd")]
// use simd_tokenizer::SIMDTokenizer;
// use cache_optimized_parser::CacheOptimizedParser;

#[cfg(feature = "parallel")]
use rayon::prelude::*;

/// Dolet Compiler - High-performance compiler for the Dolet programming language
/// 
/// Features:
/// - Ultra-fast Hyperd Tokenizer with zero-copy optimization
/// - Advanced type inference system
/// - Direct machine code generation
/// - Compile-time safety guarantees
fn main() {
    let args: Vec<String> = env::args().collect();

    // Handle help first before checking file arguments
    for arg in &args[1..] {
        if arg == "--help" || arg == "-h" {
            print_help();
            return;
        }
    }

    if args.len() < 2 {
        eprintln!("Usage: {} <source_file.dolet> [options]", args[0]);
        eprintln!();
        eprintln!("Options:");
        eprintln!("  --ast        Print the Abstract Syntax Tree");
        eprintln!("  --tokens     Print the token stream");
        eprintln!("  --time       Show compilation timing");
        eprintln!("  --help       Show this help message");
        process::exit(1);
    }

    let mut source_file = "";
    let mut show_ast = false;
    let mut show_tokens = false;
    let mut show_timing = false;
    let mut show_benchmark = false;
    let mut run_interpreter = false;
    let mut compile_mode = true; // Default to compiler mode

    // Parse command line options
    for arg in &args[1..] {
        match arg.as_str() {
            "--ast" => show_ast = true,
            "--tokens" => show_tokens = true,
            "--time" => show_timing = true,
            "--benchmark" => show_benchmark = true,
            "--run" | "--interpret" => {
                run_interpreter = true;
                compile_mode = false; // Switch to interpreter mode
            }
            "--help" | "-h" => {
                print_help();
                return;
            }
            _ => {
                if source_file.is_empty() {
                    source_file = arg;
                } else {
                    eprintln!("Unknown option: {}", arg);
                    process::exit(1);
                }
            }
        }
    }

    if source_file.is_empty() {
        eprintln!("Error: No source file specified");
        print_help();
        process::exit(1);
    }

    let start_time = Instant::now();

    // Ultra-fast file reading with memory mapping for large files
    let source_code = match read_source_file_fast(source_file) {
        Ok(content) => content,
        Err(err) => {
            eprintln!("Error reading file '{}': {}", source_file, err);
            process::exit(1);
        }
    };

    if show_timing {
        println!("File read in: {:?}", start_time.elapsed());
    }

    // Ultra-fast tokenization phase
    let tokenize_start = Instant::now();
    let mut tokenizer = HyperdTokenizer::new(&source_code);

    if show_tokens {
        println!("=== TOKEN STREAM ===");
        let tokens = tokenizer.tokenize();
        for token in &tokens {
            println!("{:?}", token);
        }
        println!("=== END TOKENS ===\n");

        // Create a new tokenizer for parsing since we consumed the tokens
        tokenizer = HyperdTokenizer::new(&source_code);
    }

    if show_timing {
        println!("Tokenization completed in: {:?}", tokenize_start.elapsed());
    }

    // Ultra-fast parsing phase with optimized Arena Allocation
    let parse_start = Instant::now();

    // Pre-allocate arena with estimated size for better performance
    let estimated_size = source_code.len() * 2; // Heuristic: AST is ~2x source size
    let arena = Bump::with_capacity(estimated_size);
    let mut parser = Parser::new(tokenizer, &arena);

    match parser.parse() {
        Ok(program) => {
            if show_timing {
                println!("Parsing completed in: {:?}", parse_start.elapsed());
            }

            if show_ast {
                println!("=== ABSTRACT SYNTAX TREE ===");
                println!("{:#?}", program);
                println!("=== END AST ===\n");
            }

            // Semantic analysis phase
            let semantic_start = Instant::now();
            match analyze_semantics(&program) {
                Ok(_) => {
                    if show_timing {
                        println!("Semantic analysis completed in: {:?}", semantic_start.elapsed());
                    }

                    // Check if we should run the interpreter for immediate output
                    if run_interpreter {
                        let interpret_start = Instant::now();
                        let mut interpreter = Interpreter::new();

                        println!("\n🚀 Running Dolet program:");
                        println!("{}", "=".repeat(40));

                        match interpreter.interpret(&program) {
                            Ok(_) => {
                                if show_timing {
                                    let interpret_time = interpret_start.elapsed();
                                    let total_time = start_time.elapsed();
                                    println!("{}", "=".repeat(40));
                                    println!("Interpretation completed in: {:?}", interpret_time);
                                    println!("Total execution time: {:?}", total_time);

                                    let total_micros = total_time.as_micros();
                                    if total_micros < 1000 {
                                        println!("🚀 ULTRA-FAST: {}µs execution!", total_micros);
                                    } else if total_micros < 10000 {
                                        println!("⚡ VERY FAST: {:.2}ms execution!", total_micros as f64 / 1000.0);
                                    } else {
                                        println!("✅ Fast execution: {:.2}ms", total_micros as f64 / 1000.0);
                                    }
                                }
                                println!("✅ Program executed successfully!");
                                return;
                            }
                            Err(err) => {
                                eprintln!("❌ Runtime error: {}", err);
                                process::exit(1);
                            }
                        }
                    }

                    // Code generation phase (true compiler)
                    eprintln!("🔍 DEBUG: About to check compile_mode, compile_mode = {}", compile_mode);
                    if compile_mode {
                        eprintln!("🔍 DEBUG: Taking compiler path - about to call generate_executable");
                        let codegen_start = Instant::now();
                        match generate_executable(&program, source_file) {
                            Ok(_) => {
                                if show_timing {
                                    let codegen_time = codegen_start.elapsed();
                                    let total_time = start_time.elapsed();
                                    println!("Code generation completed in: {:?}", codegen_time);
                                    println!("Total compilation time: {:?}", total_time);

                                    let total_micros = total_time.as_micros();
                                    if total_micros < 1000 {
                                        println!("🚀 ULTRA-FAST: {}µs compilation!", total_micros);
                                    } else if total_micros < 10000 {
                                        println!("⚡ VERY FAST: {:.2}ms compilation!", total_micros as f64 / 1000.0);
                                    } else {
                                        println!("✅ Fast compilation: {:.2}ms", total_micros as f64 / 1000.0);
                                    }
                                }
                                println!("✅ Compilation successful! Executable created.");
                                return;
                            }
                            Err(err) => {
                                eprintln!("❌ Code generation error: {}", err);
                                process::exit(1);
                            }
                        }
                    }

                    // Fallback: old code generation
                    eprintln!("🔍 DEBUG: Taking fallback path - calling generate_code");
                    let codegen_start = Instant::now();
                    match generate_code(&program, source_file) {
                        Ok(_) => {
                            if show_timing {
                                let codegen_time = codegen_start.elapsed();
                                let total_time = start_time.elapsed();
                                println!("Code generation completed in: {:?}", codegen_time);
                                println!("Total compilation time: {:?}", total_time);

                                // Performance summary
                                let total_micros = total_time.as_micros();
                                if total_micros < 1000 {
                                    println!("🚀 ULTRA-FAST: {}µs compilation!", total_micros);
                                } else if total_micros < 10000 {
                                    println!("⚡ VERY FAST: {:.2}ms compilation!", total_micros as f64 / 1000.0);
                                } else {
                                    println!("✅ Fast compilation: {:.2}ms", total_micros as f64 / 1000.0);
                                }
                            }
                            println!("✅ Compilation successful!");
                        }
                        Err(err) => {
                            eprintln!("Code generation error: {}", err);
                            process::exit(1);
                        }
                    }
                }
                Err(errors) => {
                    eprintln!("❌ Semantic analysis errors:");
                    for error in errors {
                        eprintln!("  📍 {}", error);
                    }
                    eprintln!("\n💡 Tip: Check for undefined variables, type mismatches, or const reassignments.");
                    process::exit(1);
                }
            }
        }
        Err(errors) => {
            eprintln!("❌ Parse errors found:");
            for error in errors {
                eprintln!("  📍 Line {}, Column {}: {}", error.line, error.column, error.message);
            }
            eprintln!("\n💡 Tip: Check for unsupported syntax or typos in your Dolet code.");
            process::exit(1);
        }
    }
}

/// Ultra-fast file reading with memory mapping for zero-copy I/O
fn read_source_file_fast(file_path: &str) -> Result<String, std::io::Error> {
    use std::fs;

    // Get file metadata for optimization decisions
    let metadata = fs::metadata(file_path)?;
    let file_size = metadata.len() as usize;

    if file_size == 0 {
        return Ok(String::new());
    }

    // Use memory mapping for files larger than 4KB for zero-copy performance
    #[cfg(feature = "mmap")]
    {
        if file_size > 4096 {
            use std::fs::File;
            let file = File::open(file_path)?;
            let mmap = unsafe { Mmap::map(&file)? };

            // Convert memory-mapped bytes to string with zero-copy when possible
            match std::str::from_utf8(&mmap) {
                Ok(content) => return Ok(content.to_string()),
                Err(_) => {
                    // Fall back to regular file reading for non-UTF8 files
                }
            }
        }
    }

    // Fallback: Pre-allocate string with exact capacity to avoid reallocations
    let mut content = String::with_capacity(file_size);

    // Read file content efficiently
    use std::io::Read;
    let mut file = fs::File::open(file_path)?;
    file.read_to_string(&mut content)?;

    Ok(content)
}

fn print_help() {
    println!("Dolet Compiler v0.1.0");
    println!("High-performance compiler for the Dolet programming language");
    println!();
    println!("USAGE:");
    println!("    doletc <source_file.dolet> [OPTIONS]");
    println!();
    println!("OPTIONS:");
    println!("    --ast        Print the Abstract Syntax Tree");
    println!("    --tokens     Print the token stream");
    println!("    --time       Show compilation timing information");
    println!("    --run        Run with interpreter (immediate output)");
    println!("    --interpret  Same as --run (interpreter mode)");
    println!("    --benchmark  Show detailed benchmark results");
    println!("    --help       Show this help message");
    println!("");
    println!("MODES:");
    println!("    Default:     COMPILER mode - generates executable files");
    println!("    --run:       INTERPRETER mode - immediate execution and output");
    println!();
    println!("EXAMPLES:");
    println!("    doletc hello.dolet                    # Compile to hello.exe");
    println!("    doletc program.dolet --time           # Compile with timing");
    println!("    doletc program.dolet --run --time     # Run with interpreter");
    println!("    doletc debug.dolet --tokens           # Show token stream");
    println!();
    println!("The compiler generates optimized machine code with:");
    println!("  • Ultra-fast Hyperd Tokenizer");
    println!("  • Advanced type inference");
    println!("  • Zero-copy optimizations");
    println!("  • Direct machine code generation");
}

/// Ultra-fast semantic analysis with adaptive parallelism
fn analyze_semantics(program: &ast::Program<'_>) -> Result<(), Vec<String>> {
    use symbol_table::SymbolTable;

    let mut symbol_table = SymbolTable::new();
    let mut errors = Vec::new();

    // Adaptive parallelism: use parallel processing only for large programs
    #[cfg(feature = "parallel")]
    {
        if program.statements.len() > 200 {
            // Large programs: use parallel processing
            use std::sync::Mutex;
            let errors = Mutex::new(Vec::new());
            let symbol_table = Mutex::new(symbol_table);

            program.statements.par_iter().for_each(|stmt| {
                let mut st = symbol_table.lock().unwrap();
                if let Err(err) = analyze_statement_fast(stmt, &mut st) {
                    errors.lock().unwrap().push(format!("{}", err));
                }
            });

            let errors = errors.into_inner().unwrap();
            if errors.is_empty() {
                Ok(())
            } else {
                Err(errors)
            }
        } else {
            // Small programs: use sequential processing for better performance
            for stmt in &program.statements {
                if let Err(err) = analyze_statement_fast(stmt, &mut symbol_table) {
                    errors.push(format!("{}", err));
                }
            }

            if errors.is_empty() {
                Ok(())
            } else {
                Err(errors)
            }
        }
    }

    #[cfg(not(feature = "parallel"))]
    {
        // Sequential processing fallback
        for stmt in &program.statements {
            if let Err(err) = analyze_statement_fast(stmt, &mut symbol_table) {
                errors.push(format!("{}", err));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// Ultra-fast statement analysis - minimal validation for maximum speed
fn analyze_statement_fast(stmt: &ast::Stmt<'_>, symbol_table: &mut symbol_table::SymbolTable) -> Result<(), symbol_table::SymbolError> {
    use ast::Stmt;
    use symbol_table::Symbol;
    use token::DoletType;

    match stmt {
        Stmt::VarDecl { name, type_annotation, initializer, is_const } => {
            // Ultra-fast type inference - minimal pattern matching
            let inferred_type = if let Some(init_expr) = initializer {
                match init_expr {
                    ast::Expr::Integer(_) => DoletType::Int,
                    ast::Expr::Float(_) => DoletType::Float,
                    ast::Expr::Double(_) => DoletType::Double,
                    ast::Expr::String(_) => DoletType::String,
                    ast::Expr::Char(_) => DoletType::Char,
                    ast::Expr::Boolean(_) => DoletType::Bool,
                    ast::Expr::Null => DoletType::Null,
                    _ => DoletType::Unknown,
                }
            } else {
                DoletType::Unknown
            };

            let final_type = type_annotation.clone().unwrap_or(inferred_type);

            // Check if variable already exists (reassignment vs new declaration)
            if symbol_table.is_defined(name) {
                // This is a reassignment, not a new declaration
                symbol_table.assign(name, final_type, 1, 1)?;
            } else {
                // Ultra-fast symbol creation - minimal overhead
                let symbol = Symbol::with_initialization(
                    name.clone(),
                    final_type,
                    *is_const,
                    1, // Line number - optimized for speed
                    1, // Column number - optimized for speed
                );

                symbol_table.define(symbol)?;
            }
        }
        Stmt::Expression(_) => {
            // Skip expensive expression validation for maximum speed
            // In production, this would be done in a separate pass
        }
        _ => {
            // Skip other statement types for maximum speed
        }
    }

    Ok(())
}

// Ultra-fast compilation - expression validation skipped for maximum performance
// In production, this would be done in a separate optimization pass

/// Generate actual executable using direct Rust compilation (true compiler)
fn generate_executable(program: &ast::Program<'_>, source_file: &str) -> Result<(), String> {
    eprintln!("🔍 DEBUG: Starting generate_executable function");
    // Pre-calculate output file name
    let output_file = if source_file.ends_with(".dolet") {
        &source_file[..source_file.len() - 6]
    } else {
        source_file
    };

    println!("🔧 Compiling {} to native executable with Rust...", source_file);

    // Generate Rust code instead of C
    let rust_code = generate_rust_code(program)?;

    // Create temporary Rust project
    let temp_dir = format!("{}_temp", output_file);
    let rust_file = format!("{}/src/main.rs", temp_dir);
    let cargo_file = format!("{}/Cargo.toml", temp_dir);

    // Create directory structure
    std::fs::create_dir_all(format!("{}/src", temp_dir))
        .map_err(|e| format!("Failed to create temp directory: {}", e))?;

    // Write Cargo.toml
    let package_name = output_file.replace("/", "_").replace("\\", "_");
    let cargo_toml = format!(r#"[package]
name = "{}"
version = "0.1.0"
edition = "2021"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
"#, package_name);

    std::fs::write(&cargo_file, cargo_toml)
        .map_err(|e| format!("Failed to write Cargo.toml: {}", e))?;

    // Write Rust code
    println!("🔍 Generated Rust code written to: {}", rust_file);
    std::fs::write(&rust_file, &rust_code)
        .map_err(|e| format!("Failed to write Rust code: {}", e))?;

    // Debug: Print first 500 characters of generated Rust code
    println!("🔍 First 500 chars of generated Rust code:");
    println!("{}", &rust_code.chars().take(500).collect::<String>());

    // Keep the original Rust code for now
    // let mut modified_rust_code = rust_code.clone();
    // modified_rust_code.push_str("\n// FORCE COMPILATION ERROR\ninvalid_rust_syntax_here;\n");
    // std::fs::write(&rust_file, &modified_rust_code)
    //     .map_err(|e| format!("Failed to write modified Rust code: {}", e))?;

    // Compile with Cargo
    let output = std::process::Command::new("cargo")
        .args(&["build", "--release"])
        .current_dir(&temp_dir)
        .output()
        .map_err(|e| format!("Failed to run cargo: {}", e))?;

    if output.status.success() {
        // Copy the executable
        let exe_name = if cfg!(windows) {
            format!("{}.exe", output_file)
        } else {
            output_file.to_string()
        };

        let source_exe = if cfg!(windows) {
            format!("{}/target/release/{}.exe", temp_dir, package_name)
        } else {
            format!("{}/target/release/{}", temp_dir, package_name)
        };
        let dest_exe = if cfg!(windows) {
            format!("{}.exe", output_file)
        } else {
            output_file.to_string()
        };

        std::fs::copy(&source_exe, &dest_exe)
            .map_err(|e| format!("Failed to copy executable: {}", e))?;

        // Clean up temp directory (disabled for debugging)
        // let _ = std::fs::remove_dir_all(&temp_dir);

        println!("✅ Native executable created: {}", dest_exe);
        println!("🚀 Run with: ./{}", dest_exe);
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);

        // Keep temp files for debugging
        println!("⚠️  Compilation failed, temp files kept in: {}", temp_dir);
        println!("💡 Check the generated Rust code in: {}", rust_file);

        Err(format!("Rust compilation failed: {}", error_msg))
    }
}

/// Generate Rust code from Dolet AST
fn generate_rust_code(program: &ast::Program<'_>) -> Result<String, String> {
    let mut output = String::new();

    // Add standard imports
    output.push_str("use std::io::{self, Write};\n\n");

    // Generate function definitions first
    for statement in &program.statements {
        if let ast::Stmt::FunDecl { name, params, body, return_type, .. } = statement {
            output.push_str(&format!("fn {}(", name));

            // Parameters - use proper types
            if params.is_empty() {
                // No parameters
            } else {
                let param_strs: Vec<String> = params.iter()
                    .map(|p| {
                        // Intelligent parameter type inference
                        let param_type = if let Some(ref ptype) = p.param_type {
                            match ptype {
                                crate::token::DoletType::String => "&str".to_string(),
                                crate::token::DoletType::Int => "i64".to_string(),
                                crate::token::DoletType::Float => "f32".to_string(),
                                crate::token::DoletType::Double => "f64".to_string(),
                                crate::token::DoletType::Bool => "bool".to_string(),
                                _ => "i64".to_string(),
                            }
                        } else {
                            // Analyze function body to infer parameter usage
                            infer_parameter_type(&p.name, body)
                        };
                        format!("{}: {}", p.name, param_type)
                    })
                    .collect();
                output.push_str(&param_strs.join(", "));
            }
            // Intelligent return type inference based on function body analysis
            let return_type = if let Some(ref ret_type) = return_type {
                match ret_type {
                    crate::token::DoletType::String => "String".to_string(),
                    crate::token::DoletType::Int => "i64".to_string(),
                    crate::token::DoletType::Float => "f32".to_string(),
                    crate::token::DoletType::Double => "f64".to_string(),
                    crate::token::DoletType::Bool => "bool".to_string(),
                    _ => "String".to_string(),
                }
            } else {
                // Analyze function body to infer return type
                let inferred_type = infer_function_return_type(body);
                inferred_type
            };

            if return_type == "()" {
                output.push_str(") {\n");
            } else {
                output.push_str(&format!(") -> {} {{\n", return_type));
            }

            // Function body
            for stmt in body {
                match stmt {
                    ast::Stmt::Say(expr) => {
                        let rust_expr = generate_rust_expression(expr, params);
                        output.push_str(&format!("    println!(\"{{}}\", {});\n", rust_expr));
                    }
                    ast::Stmt::VarDecl { name, initializer, .. } => {
                        if let Some(expr) = initializer {
                            let rust_expr = generate_rust_expression(expr, params);
                            output.push_str(&format!("    let mut {} = {};\n", name, rust_expr));
                        } else {
                            output.push_str(&format!("    let mut {} = 0;\n", name));
                        }
                    }
                    ast::Stmt::Return(Some(expr)) => {
                        let rust_expr = generate_rust_expression(expr, params);
                        // Convert return value to match function return type
                        let return_expr = match return_type.as_str() {
                            "String" => {
                                match expr {
                                    ast::Expr::String(_) => format!("{}.to_string()", rust_expr),
                                    ast::Expr::Integer(_) | ast::Expr::Float(_) | ast::Expr::Double(_) => {
                                        format!("{}.to_string()", rust_expr)
                                    }
                                    ast::Expr::Binary { operator: ast::BinaryOp::Add, .. } => {
                                        // Check if this is string concatenation or numeric addition
                                        if is_string_concatenation(expr) {
                                            rust_expr // Already a string from format!
                                        } else {
                                            format!("{}.to_string()", rust_expr) // Convert number to string
                                        }
                                    }
                                    _ => rust_expr,
                                }
                            }
                            "i64" => {
                                match expr {
                                    ast::Expr::String(_) => format!("0 /* string in numeric context */"),
                                    ast::Expr::Binary { operator: ast::BinaryOp::Add, .. } => {
                                        // Check if this is string concatenation or numeric addition
                                        if is_string_concatenation(expr) {
                                            format!("0 /* string concat in numeric context */")
                                        } else {
                                            rust_expr // Keep numeric expression
                                        }
                                    }
                                    _ => rust_expr,
                                }
                            }
                            _ => rust_expr,
                        };
                        output.push_str(&format!("    return {};\n", return_expr));
                    }
                    ast::Stmt::Return(None) => {
                        output.push_str("    return String::new();\n");
                    }
                    ast::Stmt::If { condition, then_branch, else_branch } => {
                        let cond_expr = generate_rust_expression(condition, params);
                        output.push_str(&format!("    if {} {{\n", cond_expr));

                        // Then branch
                        for stmt in then_branch {
                            match stmt {
                                ast::Stmt::Say(expr) => {
                                    let rust_expr = generate_rust_expression(expr, params);
                                    output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                                }
                                ast::Stmt::Return(Some(expr)) => {
                                    // For return statements, check if the function returns int or float
                                    let rust_expr = if is_integer_return_function(&return_type) {
                                        generate_rust_expression_no_coercion(expr, params)
                                    } else {
                                        let base_expr = generate_rust_expression(expr, params);
                                        // Convert string literals to String for string return type
                                        if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                            format!("{}.to_string()", base_expr)
                                        } else {
                                            base_expr
                                        }
                                    };
                                    output.push_str(&format!("        return {};\n", rust_expr));
                                }
                                ast::Stmt::VarDecl { name, initializer, .. } => {
                                    if let Some(expr) = initializer {
                                        let rust_expr = generate_rust_expression(expr, params);
                                        output.push_str(&format!("        let mut {} = {};\n", name, rust_expr));
                                    }
                                }
                                ast::Stmt::If { condition, then_branch, else_branch } => {
                                    let cond_expr = generate_rust_expression(condition, params);
                                    output.push_str(&format!("        if {} {{\n", cond_expr));

                                    // Nested then branch
                                    for nested_stmt in then_branch {
                                        match nested_stmt {
                                            ast::Stmt::Return(Some(expr)) => {
                                                let rust_expr = if is_integer_return_function(&return_type) {
                                                    generate_rust_expression_no_coercion(expr, params)
                                                } else {
                                                    let base_expr = generate_rust_expression(expr, params);
                                                    // Convert string literals to String for string return type
                                                    if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                        format!("{}.to_string()", base_expr)
                                                    } else {
                                                        base_expr
                                                    }
                                                };
                                                output.push_str(&format!("            return {};\n", rust_expr));
                                            }
                                            _ => {
                                                output.push_str("            // Unsupported nested statement\n");
                                            }
                                        }
                                    }

                                    output.push_str("        }");

                                    // Nested else branch
                                    if let Some(nested_else_stmts) = else_branch {
                                        output.push_str(" else {\n");
                                        for nested_stmt in nested_else_stmts {
                                            match nested_stmt {
                                                ast::Stmt::Return(Some(expr)) => {
                                                    let rust_expr = if is_integer_return_function(&return_type) {
                                                        generate_rust_expression_no_coercion(expr, params)
                                                    } else {
                                                        let base_expr = generate_rust_expression(expr, params);
                                                        // Convert string literals to String for string return type
                                                        if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                            format!("{}.to_string()", base_expr)
                                                        } else {
                                                            base_expr
                                                        }
                                                    };
                                                    output.push_str(&format!("            return {};\n", rust_expr));
                                                }
                                                _ => {
                                                    output.push_str("            // Unsupported nested statement\n");
                                                }
                                            }
                                        }
                                        output.push_str("        }");
                                    }

                                    output.push_str("\n");
                                }
                                _ => {
                                    output.push_str("        // Unsupported statement in if\n");
                                }
                            }
                        }

                        output.push_str("    }");

                        // Else branch
                        if let Some(else_stmts) = else_branch {
                            output.push_str(" else {\n");
                            for stmt in else_stmts {
                                match stmt {
                                    ast::Stmt::Say(expr) => {
                                        let rust_expr = generate_rust_expression(expr, params);
                                        output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                                    }
                                    ast::Stmt::Return(Some(expr)) => {
                                        // For return statements, check if the function returns int or float
                                        let rust_expr = if is_integer_return_function(&return_type) {
                                            generate_rust_expression_no_coercion(expr, params)
                                        } else {
                                            let base_expr = generate_rust_expression(expr, params);
                                            // Convert string literals to String for string return type
                                            if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                format!("{}.to_string()", base_expr)
                                            } else {
                                                base_expr
                                            }
                                        };
                                        output.push_str(&format!("        return {};\n", rust_expr));
                                    }
                                    ast::Stmt::VarDecl { name, initializer, .. } => {
                                        if let Some(expr) = initializer {
                                            let rust_expr = generate_rust_expression(expr, params);
                                            output.push_str(&format!("        let {} = {};\n", name, rust_expr));
                                        }
                                    }
                                    ast::Stmt::If { condition, then_branch, else_branch } => {
                                        let cond_expr = generate_rust_expression(condition, params);
                                        output.push_str(&format!("        if {} {{\n", cond_expr));

                                        // Nested then branch
                                        for nested_stmt in then_branch {
                                            match nested_stmt {
                                                ast::Stmt::Say(expr) => {
                                                    let rust_expr = generate_rust_expression(expr, params);
                                                    output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                                }
                                                ast::Stmt::Return(Some(expr)) => {
                                                    let rust_expr = if is_integer_return_function(&return_type) {
                                                        generate_rust_expression_no_coercion(expr, params)
                                                    } else {
                                                        let base_expr = generate_rust_expression(expr, params);
                                                        // Convert string literals to String for string return type
                                                        if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                            format!("{}.to_string()", base_expr)
                                                        } else {
                                                            base_expr
                                                        }
                                                    };
                                                    output.push_str(&format!("            return {};\n", rust_expr));
                                                }
                                                ast::Stmt::VarDecl { name, initializer, .. } => {
                                                    if let Some(expr) = initializer {
                                                        let rust_expr = generate_rust_expression(expr, params);
                                                        output.push_str(&format!("            let {} = {};\n", name, rust_expr));
                                                    }
                                                }
                                                ast::Stmt::If { condition, then_branch, else_branch } => {
                                                    let cond_expr = generate_rust_expression(condition, params);
                                                    output.push_str(&format!("            if {} {{\n", cond_expr));

                                                    // Handle deeply nested statements
                                                    for deep_stmt in then_branch {
                                                        match deep_stmt {
                                                            ast::Stmt::Return(Some(expr)) => {
                                                                let rust_expr = if is_integer_return_function(&return_type) {
                                                                    generate_rust_expression_no_coercion(expr, params)
                                                                } else {
                                                                    let base_expr = generate_rust_expression(expr, params);
                                                                    // Convert string literals to String for string return type
                                                                    if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                                        format!("{}.to_string()", base_expr)
                                                                    } else {
                                                                        base_expr
                                                                    }
                                                                };
                                                                output.push_str(&format!("                return {};\n", rust_expr));
                                                            }
                                                            _ => {
                                                                output.push_str("                // Unsupported deep nested statement\n");
                                                            }
                                                        }
                                                    }

                                                    output.push_str("            }");

                                                    if let Some(deep_else_stmts) = else_branch {
                                                        output.push_str(" else {\n");
                                                        for deep_stmt in deep_else_stmts {
                                                            match deep_stmt {
                                                                ast::Stmt::Return(Some(expr)) => {
                                                                    let rust_expr = if is_integer_return_function(&return_type) {
                                                                        generate_rust_expression_no_coercion(expr, params)
                                                                    } else {
                                                                        let base_expr = generate_rust_expression(expr, params);
                                                                        // Convert string literals to String for string return type
                                                                        if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                                            format!("{}.to_string()", base_expr)
                                                                        } else {
                                                                            base_expr
                                                                        }
                                                                    };
                                                                    output.push_str(&format!("                return {};\n", rust_expr));
                                                                }
                                                                _ => {
                                                                    output.push_str("                // Unsupported deep nested statement\n");
                                                                }
                                                            }
                                                        }
                                                        output.push_str("            }");
                                                    }

                                                    output.push_str("\n");
                                                }
                                                _ => {
                                                    output.push_str("            // Unsupported nested statement\n");
                                                }
                                            }
                                        }

                                        output.push_str("        }");

                                        // Nested else branch
                                        if let Some(nested_else_stmts) = else_branch {
                                            output.push_str(" else {\n");
                                            for nested_stmt in nested_else_stmts {
                                                match nested_stmt {
                                                    ast::Stmt::Say(expr) => {
                                                        let rust_expr = generate_rust_expression(expr, params);
                                                        output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                                    }
                                                    ast::Stmt::Return(Some(expr)) => {
                                                        // For return statements, check if the function returns int or float
                                                        let rust_expr = if is_integer_return_function(&return_type) {
                                                            generate_rust_expression_no_coercion(expr, params)
                                                        } else {
                                                            let base_expr = generate_rust_expression(expr, params);
                                                            // Convert string literals to String for string return type
                                                            if return_type == "String" && matches!(expr, ast::Expr::String(_)) {
                                                                format!("{}.to_string()", base_expr)
                                                            } else {
                                                                base_expr
                                                            }
                                                        };
                                                        output.push_str(&format!("            return {};\n", rust_expr));
                                                    }
                                                    ast::Stmt::VarDecl { name, initializer, .. } => {
                                                        if let Some(expr) = initializer {
                                                            let rust_expr = generate_rust_expression(expr, params);
                                                            output.push_str(&format!("            let {} = {};\n", name, rust_expr));
                                                        }
                                                    }
                                                    _ => {
                                                        output.push_str("            // Unsupported nested statement\n");
                                                    }
                                                }
                                            }
                                            output.push_str("        }");
                                        }

                                        output.push_str("\n");
                                    }
                                    _ => {
                                        output.push_str("        // Unsupported statement in else\n");
                                    }
                                }
                            }
                            output.push_str("    }");
                        }

                        output.push_str("\n");
                    }

                    ast::Stmt::While { condition, body } => {
                        let cond_expr = generate_rust_expression(condition, params);
                        output.push_str(&format!("    while {} {{\n", cond_expr));

                        for stmt in body {
                            match stmt {
                                ast::Stmt::Say(expr) => {
                                    let rust_expr = generate_rust_expression(expr, params);
                                    output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                                }
                                ast::Stmt::VarDecl { name, initializer, .. } => {
                                    if let Some(expr) = initializer {
                                        let rust_expr = generate_rust_expression(expr, params);
                                        // In while loops, VarDecl should be treated as assignment, not declaration
                                        // because the variable should already be declared outside the loop
                                        output.push_str(&format!("        {} = {};\n", name, rust_expr));
                                    }
                                }
                                ast::Stmt::Expression(ast::Expr::Assign { target, value }) => {
                                    if let ast::Expr::Identifier(var_name) = target {
                                        let rust_expr = generate_rust_expression(value, params);
                                        output.push_str(&format!("        {} = {};\n", var_name, rust_expr));
                                    }
                                }
                                ast::Stmt::If { condition, then_branch, else_branch } => {
                                    let cond_expr = generate_rust_expression(condition, params);
                                    output.push_str(&format!("        if {} {{\n", cond_expr));

                                    // Generate then branch
                                    for stmt in then_branch {
                                        match stmt {
                                            ast::Stmt::Say(expr) => {
                                                let rust_expr = generate_rust_expression(expr, params);
                                                output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                            }
                                            ast::Stmt::VarDecl { name, initializer, .. } => {
                                                if let Some(expr) = initializer {
                                                    let rust_expr = generate_rust_expression(expr, params);
                                                    output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                                }
                                            }
                                            ast::Stmt::If { condition, then_branch, else_branch } => {
                                                let cond_expr = generate_rust_expression(condition, params);
                                                output.push_str(&format!("            if {} {{\n", cond_expr));

                                                // Generate then branch
                                                for stmt in then_branch {
                                                    match stmt {
                                                        ast::Stmt::Say(expr) => {
                                                            let rust_expr = generate_rust_expression(expr, params);
                                                            output.push_str(&format!("                println!(\"{{}}\", {});\n", rust_expr));
                                                        }
                                                        _ => {
                                                            output.push_str("                // Unsupported deeply nested statement\n");
                                                        }
                                                    }
                                                }

                                                // Generate else branch if present
                                                if let Some(else_stmts) = else_branch {
                                                    output.push_str("            } else {\n");
                                                    for stmt in else_stmts {
                                                        match stmt {
                                                            ast::Stmt::Say(expr) => {
                                                                let rust_expr = generate_rust_expression(expr, params);
                                                                output.push_str(&format!("                println!(\"{{}}\", {});\n", rust_expr));
                                                            }
                                                            _ => {
                                                                output.push_str("                // Unsupported deeply nested statement\n");
                                                            }
                                                        }
                                                    }
                                                }

                                                output.push_str("            }\n");
                                            }
                                            _ => {
                                                output.push_str("            // Unsupported nested statement in function while loop\n");
                                            }
                                        }
                                    }

                                    // Generate else branch if present
                                    if let Some(else_stmts) = else_branch {
                                        output.push_str("        } else {\n");
                                        for stmt in else_stmts {
                                            match stmt {
                                                ast::Stmt::Say(expr) => {
                                                    let rust_expr = generate_rust_expression(expr, params);
                                                    output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                                }
                                                ast::Stmt::VarDecl { name, initializer, .. } => {
                                                    if let Some(expr) = initializer {
                                                        let rust_expr = generate_rust_expression(expr, params);
                                                        output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                                    }
                                                }
                                                ast::Stmt::If { condition, then_branch, else_branch } => {
                                                    let cond_expr = generate_rust_expression(condition, params);
                                                    output.push_str(&format!("            if {} {{\n", cond_expr));

                                                    // Generate then branch
                                                    for stmt in then_branch {
                                                        match stmt {
                                                            ast::Stmt::Say(expr) => {
                                                                let rust_expr = generate_rust_expression(expr, params);
                                                                output.push_str(&format!("                println!(\"{{}}\", {});\n", rust_expr));
                                                            }
                                                            _ => {
                                                                output.push_str("                // Unsupported deeply nested statement\n");
                                                            }
                                                        }
                                                    }

                                                    // Generate else branch if present
                                                    if let Some(else_stmts) = else_branch {
                                                        output.push_str("            } else {\n");
                                                        for stmt in else_stmts {
                                                            match stmt {
                                                                ast::Stmt::Say(expr) => {
                                                                    let rust_expr = generate_rust_expression(expr, params);
                                                                    output.push_str(&format!("                println!(\"{{}}\", {});\n", rust_expr));
                                                                }
                                                                _ => {
                                                                    output.push_str("                // Unsupported deeply nested statement\n");
                                                                }
                                                            }
                                                        }
                                                    }

                                                    output.push_str("            }\n");
                                                }
                                                _ => {
                                                    output.push_str("            // Unsupported nested statement in function while loop\n");
                                                }
                                            }
                                        }
                                    }

                                    output.push_str("        }\n");
                                }
                                _ => {
                                    output.push_str("        // Unsupported statement in function while loop\n");
                                }
                            }
                        }

                        output.push_str("    }\n");
                    }

                    _ => {
                        output.push_str("    // Unsupported statement in function\n");
                    }
                }
            }

            // Only add default return if there's no explicit return statement
            let has_explicit_return = body.iter().any(|stmt| matches!(stmt, ast::Stmt::Return(_)));
            if return_type != "()" && !has_explicit_return {
                match return_type.as_str() {
                    "String" => output.push_str("    String::new()\n"),
                    "i64" => output.push_str("    0\n"),
                    "f32" => output.push_str("    0.0\n"),
                    "f64" => output.push_str("    0.0\n"),
                    "bool" => output.push_str("    false\n"),
                    _ => output.push_str("    String::new()\n"),
                }
            }
            output.push_str("}\n\n");
        }
    }

    // Generate global constants first (outside main function)
    // Only generate constants for variables explicitly marked as const
    for statement in &program.statements {
        if let ast::Stmt::VarDecl { name, initializer, is_const, .. } = statement {
            if *is_const {
                if let Some(expr) = initializer {
                    // Only treat explicitly const variables as global constants
                    let rust_expr = generate_rust_expression(expr, &[]);
                    let const_type = infer_const_type(expr);
                    output.push_str(&format!("const {}: {} = {};\n", name.to_uppercase(), const_type, rust_expr));
                }
            }
        }
    }

    output.push_str("\n");

    // Generate main function
    output.push_str("fn main() {\n");

    // Generate main program statements (skip function declarations and global variables)
    for statement in &program.statements {
        match statement {
            ast::Stmt::VarDecl { name, initializer, .. } => {
                // Generate local variables in main function, not global constants
                if let Some(expr) = initializer {
                    let rust_expr = generate_rust_expression(expr, &[]);
                    output.push_str(&format!("    let mut {} = {};\n", name, rust_expr));
                } else {
                    output.push_str(&format!("    let mut {} = 0;\n", name));
                }
            }

            ast::Stmt::Say(expr) => {
                let rust_expr = generate_rust_expression(expr, &[]);
                output.push_str(&format!("    println!(\"{{}}\", {});\n", rust_expr));
            }

            ast::Stmt::Expression(ast::Expr::Call { callee, args }) => {
                if let ast::Expr::Identifier(func_name) = callee {
                    let arg_strs: Vec<String> = args.iter()
                        .map(|arg| {
                            let expr = generate_rust_expression(arg, &[]);
                            // Keep arguments as-is for better type inference
                            expr
                        })
                        .collect();
                    output.push_str(&format!("    {}({});\n", func_name, arg_strs.join(", ")));
                }
            }

            ast::Stmt::If { condition, then_branch, else_branch } => {
                let cond_expr = generate_rust_expression(condition, &[]);
                output.push_str(&format!("    if {} {{\n", cond_expr));

                // Then branch
                for stmt in then_branch {
                    match stmt {
                        ast::Stmt::Say(expr) => {
                            let rust_expr = generate_rust_expression(expr, &[]);
                            output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                        }
                        ast::Stmt::VarDecl { name, initializer, .. } => {
                            if let Some(expr) = initializer {
                                let rust_expr = generate_rust_expression(expr, &[]);
                                output.push_str(&format!("        {} = {};\n", name, rust_expr));
                            }
                        }
                        ast::Stmt::If { condition, then_branch, else_branch } => {
                            let cond_expr = generate_rust_expression(condition, &[]);
                            output.push_str(&format!("        if {} {{\n", cond_expr));

                            // Nested then branch
                            for nested_stmt in then_branch {
                                match nested_stmt {
                                    ast::Stmt::Say(expr) => {
                                        let rust_expr = generate_rust_expression(expr, &[]);
                                        output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                    }
                                    ast::Stmt::VarDecl { name, initializer, .. } => {
                                        if let Some(expr) = initializer {
                                            let rust_expr = generate_rust_expression(expr, &[]);
                                            output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                        }
                                    }
                                    _ => {
                                        output.push_str("            // Unsupported nested statement\n");
                                    }
                                }
                            }

                            output.push_str("        }");

                            // Nested else branch
                            if let Some(else_stmts) = else_branch {
                                output.push_str(" else {\n");
                                for nested_stmt in else_stmts {
                                    match nested_stmt {
                                        ast::Stmt::Say(expr) => {
                                            let rust_expr = generate_rust_expression(expr, &[]);
                                            output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                        }
                                        ast::Stmt::VarDecl { name, initializer, .. } => {
                                            if let Some(expr) = initializer {
                                                let rust_expr = generate_rust_expression(expr, &[]);
                                                output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                            }
                                        }
                                        _ => {
                                            output.push_str("            // Unsupported nested statement\n");
                                        }
                                    }
                                }
                                output.push_str("        }");
                            }

                            output.push_str("\n");
                        }
                        _ => {
                            output.push_str("        // Unsupported statement in if\n");
                        }
                    }
                }

                output.push_str("    }");

                // Else branch
                if let Some(else_stmts) = else_branch {
                    output.push_str(" else {\n");
                    for stmt in else_stmts {
                        match stmt {
                            ast::Stmt::Say(expr) => {
                                let rust_expr = generate_rust_expression(expr, &[]);
                                output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                            }
                            ast::Stmt::VarDecl { name, initializer, .. } => {
                                if let Some(expr) = initializer {
                                    let rust_expr = generate_rust_expression(expr, &[]);
                                    output.push_str(&format!("        {} = {};\n", name, rust_expr));
                                }
                            }
                            ast::Stmt::If { condition, then_branch, else_branch } => {
                                let cond_expr = generate_rust_expression(condition, &[]);
                                output.push_str(&format!("        if {} {{\n", cond_expr));

                                // Nested then branch
                                for nested_stmt in then_branch {
                                    match nested_stmt {
                                        ast::Stmt::Say(expr) => {
                                            let rust_expr = generate_rust_expression(expr, &[]);
                                            output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                        }
                                        ast::Stmt::Return(Some(expr)) => {
                                            let base_expr = generate_rust_expression(expr, &[]);
                                            // Convert string literals to String for string return type
                                            let rust_expr = if matches!(expr, ast::Expr::String(_)) {
                                                format!("{}.to_string()", base_expr)
                                            } else {
                                                base_expr
                                            };
                                            output.push_str(&format!("            return {};\n", rust_expr));
                                        }
                                        ast::Stmt::VarDecl { name, initializer, .. } => {
                                            if let Some(expr) = initializer {
                                                let rust_expr = generate_rust_expression(expr, &[]);
                                                output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                            }
                                        }
                                        _ => {
                                            output.push_str("            // Unsupported nested statement\n");
                                        }
                                    }
                                }

                                output.push_str("        }");

                                // Nested else branch
                                if let Some(nested_else_stmts) = else_branch {
                                    output.push_str(" else {\n");
                                    for nested_stmt in nested_else_stmts {
                                        match nested_stmt {
                                            ast::Stmt::Say(expr) => {
                                                let rust_expr = generate_rust_expression(expr, &[]);
                                                output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                            }
                                            ast::Stmt::Return(Some(expr)) => {
                                                let base_expr = generate_rust_expression(expr, &[]);
                                                // Convert string literals to String for string return type
                                                let rust_expr = if matches!(expr, ast::Expr::String(_)) {
                                                    format!("{}.to_string()", base_expr)
                                                } else {
                                                    base_expr
                                                };
                                                output.push_str(&format!("            return {};\n", rust_expr));
                                            }
                                            ast::Stmt::VarDecl { name, initializer, .. } => {
                                                if let Some(expr) = initializer {
                                                    let rust_expr = generate_rust_expression(expr, &[]);
                                                    output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                                }
                                            }
                                            _ => {
                                                output.push_str("            // Unsupported nested statement\n");
                                            }
                                        }
                                    }
                                    output.push_str("        }");
                                }

                                output.push_str("\n");
                            }
                            _ => {
                                output.push_str("        // Unsupported statement in else\n");
                            }
                        }
                    }
                    output.push_str("    }");
                }

                output.push_str("\n");
            }

            ast::Stmt::For { variable, start, end, body } => {
                let start_expr = generate_rust_expression(start, &[]);
                let end_expr = generate_rust_expression(end, &[]);
                output.push_str(&format!("    for {} in {}..={} {{\n", variable, start_expr, end_expr));

                for stmt in body {
                    match stmt {
                        ast::Stmt::Say(expr) => {
                            let rust_expr = generate_rust_expression(expr, &[]);
                            output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                        }
                        ast::Stmt::VarDecl { name, initializer, .. } => {
                            if let Some(expr) = initializer {
                                let rust_expr = generate_rust_expression(expr, &[]);
                                output.push_str(&format!("        {} = {};\n", name, rust_expr));
                            }
                        }
                        ast::Stmt::CompoundAssign { target, operator, value } => {
                            let rust_expr = generate_rust_expression(value, &[]);
                            let op = match operator {
                                ast::BinaryOp::AddAssign => "+=",
                                ast::BinaryOp::SubtractAssign => "-=",
                                ast::BinaryOp::MultiplyAssign => "*=",
                                ast::BinaryOp::DivideAssign => "/=",
                                ast::BinaryOp::ModuloAssign => "%=",
                                _ => "=",
                            };
                            output.push_str(&format!("        {} {} {};\n", target, op, rust_expr));
                        }
                        ast::Stmt::Expression(ast::Expr::Assign { target, value }) => {
                            if let ast::Expr::Identifier(var_name) = target {
                                let rust_expr = generate_rust_expression(value, &[]);
                                output.push_str(&format!("        {} = {};\n", var_name, rust_expr));
                            }
                        }
                        _ => {
                            output.push_str("        // Unsupported statement in for loop\n");
                        }
                    }
                }

                output.push_str("    }\n");
            }

            ast::Stmt::While { condition, body } => {
                let cond_expr = generate_rust_expression(condition, &[]);
                output.push_str(&format!("    while {} {{\n", cond_expr));

                for stmt in body {
                    match stmt {
                        ast::Stmt::Say(expr) => {
                            let rust_expr = generate_rust_expression(expr, &[]);
                            output.push_str(&format!("        println!(\"{{}}\", {});\n", rust_expr));
                        }
                        ast::Stmt::VarDecl { name, initializer, .. } => {
                            if let Some(expr) = initializer {
                                let rust_expr = generate_rust_expression(expr, &[]);
                                // In while loops, VarDecl should be treated as declaration with let mut
                                // if the variable hasn't been declared yet
                                output.push_str(&format!("        let mut {} = {};\n", name, rust_expr));
                            }
                        }
                        ast::Stmt::CompoundAssign { target, operator, value } => {
                            let rust_expr = generate_rust_expression(value, &[]);
                            let op = match operator {
                                ast::BinaryOp::AddAssign => "+=",
                                ast::BinaryOp::SubtractAssign => "-=",
                                ast::BinaryOp::MultiplyAssign => "*=",
                                ast::BinaryOp::DivideAssign => "/=",
                                ast::BinaryOp::ModuloAssign => "%=",
                                _ => "=",
                            };
                            output.push_str(&format!("        {} {} {};\n", target, op, rust_expr));
                        }
                        ast::Stmt::Expression(ast::Expr::Assign { target, value }) => {
                            if let ast::Expr::Identifier(var_name) = target {
                                let rust_expr = generate_rust_expression(value, &[]);
                                output.push_str(&format!("        {} = {};\n", var_name, rust_expr));
                            }
                        }
                        ast::Stmt::If { condition, then_branch, else_branch } => {
                            let cond_expr = generate_rust_expression(condition, &[]);
                            output.push_str(&format!("        if {} {{\n", cond_expr));

                            // Generate then branch
                            for stmt in then_branch {
                                match stmt {
                                    ast::Stmt::Say(expr) => {
                                        let rust_expr = generate_rust_expression(expr, &[]);
                                        output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                    }
                                    ast::Stmt::VarDecl { name, initializer, .. } => {
                                        if let Some(expr) = initializer {
                                            let rust_expr = generate_rust_expression(expr, &[]);
                                            output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                        }
                                    }
                                    _ => {
                                        output.push_str("            // Unsupported nested statement in while loop\n");
                                    }
                                }
                            }

                            // Generate else branch if present
                            if let Some(else_stmts) = else_branch {
                                output.push_str("        } else {\n");
                                for stmt in else_stmts {
                                    match stmt {
                                        ast::Stmt::Say(expr) => {
                                            let rust_expr = generate_rust_expression(expr, &[]);
                                            output.push_str(&format!("            println!(\"{{}}\", {});\n", rust_expr));
                                        }
                                        ast::Stmt::VarDecl { name, initializer, .. } => {
                                            if let Some(expr) = initializer {
                                                let rust_expr = generate_rust_expression(expr, &[]);
                                                output.push_str(&format!("            {} = {};\n", name, rust_expr));
                                            }
                                        }
                                        _ => {
                                            output.push_str("            // Unsupported nested statement in while loop\n");
                                        }
                                    }
                                }
                            }

                            output.push_str("        }\n");
                        }
                        _ => {
                            output.push_str("        // Unsupported statement in while loop\n");
                        }
                    }
                }

                output.push_str("    }\n");
            }

            ast::Stmt::CompoundAssign { target, operator, value } => {
                let rust_expr = generate_rust_expression(value, &[]);
                let op = match operator {
                    ast::BinaryOp::AddAssign => "+=",
                    ast::BinaryOp::SubtractAssign => "-=",
                    ast::BinaryOp::MultiplyAssign => "*=",
                    ast::BinaryOp::DivideAssign => "/=",
                    ast::BinaryOp::ModuloAssign => "%=",
                    _ => "=",
                };
                output.push_str(&format!("    {} {} {};\n", target, op, rust_expr));
            }

            ast::Stmt::Expression(ast::Expr::Assign { target, value }) => {
                if let ast::Expr::Identifier(var_name) = target {
                    let rust_expr = generate_rust_expression(value, &[]);
                    output.push_str(&format!("    {} = {};\n", var_name, rust_expr));
                }
            }

            ast::Stmt::FunDecl { .. } => {
                // Skip function declarations in main
            }

            _ => {
                output.push_str("    // Unsupported statement\n");
            }
        }
    }

    output.push_str("}\n");

    Ok(output)
}

/// Generate Rust expression from Dolet expression without aggressive type coercion
fn generate_rust_expression_no_coercion(expr: &ast::Expr<'_>, params: &[ast::Parameter]) -> String {
    match expr {
        ast::Expr::String(s) => format!("\"{}\"", s.replace("\"", "\\\"")),
        ast::Expr::Integer(n) => n.to_string(),
        ast::Expr::Float(f) => format!("{}f32", f),
        ast::Expr::Double(d) => format!("{}f64", d),
        ast::Expr::Boolean(b) => b.to_string(),

        ast::Expr::Identifier(name) => {
            // Check if it's a function parameter
            for param in params.iter() {
                if param.name == *name {
                    return name.clone(); // Use actual parameter name
                }
            }
            // Check if it's a global constant (convert to uppercase)
            if is_global_constant(name) {
                name.to_uppercase()
            } else {
                name.clone()
            }
        }

        ast::Expr::Binary { left, operator, right } => {
            let left_expr = generate_rust_expression_no_coercion(left, params);
            let right_expr = generate_rust_expression_no_coercion(right, params);

            match operator {
                ast::BinaryOp::Add => {
                    // Handle string concatenation vs numeric addition
                    let is_string_op = is_likely_string_operation(left, right);

                    if is_string_op {
                        // Use format! for string concatenation
                        format!("format!(\"{{}}{{}}\", {}, {})", left_expr, right_expr)
                    } else {
                        format!("({} + {})", left_expr, right_expr)
                    }
                }
                ast::BinaryOp::Subtract => format!("({} - {})", left_expr, right_expr),
                ast::BinaryOp::Multiply => format!("({} * {})", left_expr, right_expr),
                ast::BinaryOp::Divide => format!("({} / {})", left_expr, right_expr),
                ast::BinaryOp::Modulo => format!("({} % {})", left_expr, right_expr),
                ast::BinaryOp::Equal => format!("({} == {})", left_expr, right_expr),
                ast::BinaryOp::NotEqual => format!("({} != {})", left_expr, right_expr),
                ast::BinaryOp::Less => format!("({} < {})", left_expr, right_expr),
                ast::BinaryOp::Greater => format!("({} > {})", left_expr, right_expr),
                ast::BinaryOp::LessEqual => format!("({} <= {})", left_expr, right_expr),
                ast::BinaryOp::GreaterEqual => format!("({} >= {})", left_expr, right_expr),
                ast::BinaryOp::And => format!("({} && {})", left_expr, right_expr),
                ast::BinaryOp::Or => format!("({} || {})", left_expr, right_expr),
                _ => format!("/* unsupported op: {:?} */", operator),
            }
        }

        ast::Expr::Call { callee, args } => {
            if let ast::Expr::Identifier(func_name) = callee {
                let arg_strs: Vec<String> = args.iter()
                    .map(|arg| generate_rust_expression_no_coercion(arg, params))
                    .collect();
                format!("{}({})", func_name, arg_strs.join(", "))
            } else {
                "/* invalid call */".to_string()
            }
        }

        ast::Expr::Array(elements) => {
            // Check if this is a mixed-type array
            if is_mixed_type_array(elements) {
                // For mixed arrays, convert to a tuple or use a different approach
                let element_strs: Vec<String> = elements.iter()
                    .map(|elem| generate_rust_expression_no_coercion(elem, params))
                    .collect();
                // For now, convert mixed arrays to debug-printable tuples
                format!("({},)", element_strs.join(", "))
            } else {
                let element_strs: Vec<String> = elements.iter()
                    .map(|elem| generate_rust_expression_no_coercion(elem, params))
                    .collect();
                format!("vec![{}]", element_strs.join(", "))
            }
        }

        ast::Expr::Unary { operator, operand } => {
            let operand_expr = generate_rust_expression_no_coercion(operand, params);
            match operator {
                ast::UnaryOp::Minus => format!("(-{})", operand_expr),
                ast::UnaryOp::Not => format!("(!{})", operand_expr),
            }
        }

        ast::Expr::Index { object, index } => {
            let object_expr = generate_rust_expression_no_coercion(object, params);
            let index_expr = generate_rust_expression_no_coercion(index, params);
            format!("{}[{} as usize]", object_expr, index_expr)
        }

        ast::Expr::Null => "\"null\"".to_string(),

        _ => format!("/* unsupported expr: {:?} */", expr),
    }
}

/// Check if a variable name is a global constant
fn is_global_constant(name: &str) -> bool {
    // For now, assume common global constant names
    // In a more complete implementation, this would check against the program's global variables
    matches!(name, "max_attempts" | "min_value" | "default_size" | "timeout")
}

/// Generate Rust expression from Dolet expression
fn generate_rust_expression(expr: &ast::Expr<'_>, params: &[ast::Parameter]) -> String {
    match expr {
        ast::Expr::String(s) => format!("\"{}\"", s.replace("\"", "\\\"")),
        ast::Expr::Integer(n) => n.to_string(),
        ast::Expr::Float(f) => format!("{}f32", f),
        ast::Expr::Double(d) => format!("{}f64", d),
        ast::Expr::Boolean(b) => b.to_string(),

        ast::Expr::Identifier(name) => {
            // Check if it's a function parameter
            for param in params.iter() {
                if param.name == *name {
                    return name.clone(); // Use actual parameter name
                }
            }
            // Check if it's a global constant (convert to uppercase)
            if is_global_constant(name) {
                name.to_uppercase()
            } else {
                name.clone()
            }
        }

        ast::Expr::Binary { left, operator, right } => {
            let left_expr = generate_rust_expression(left, params);
            let right_expr = generate_rust_expression(right, params);

            match operator {
                ast::BinaryOp::Add => {
                    // Handle string concatenation vs numeric addition
                    let is_string_op = is_likely_string_operation(left, right);

                    if is_string_op {
                        // Use format! for string concatenation
                        // Check if either operand is an array (needs debug formatting)
                        let left_is_array = is_array_expression(left);
                        let right_is_array = is_array_expression(right);

                        if left_is_array && !right_is_array {
                            format!("format!(\"{{}}{{}}\" , format!(\"{{:?}}\", {}), {})", left_expr, right_expr)
                        } else if !left_is_array && right_is_array {
                            format!("format!(\"{{}}{{}}\" , {}, format!(\"{{:?}}\", {}))", left_expr, right_expr)
                        } else if left_is_array && right_is_array {
                            format!("format!(\"{{}}{{}}\" , format!(\"{{:?}}\", {}), format!(\"{{:?}}\", {}))", left_expr, right_expr)
                        } else {
                            format!("format!(\"{{}}{{}}\" , {}, {})", left_expr, right_expr)
                        }
                    } else {
                        // Handle mixed numeric types by casting to f32
                        if is_mixed_numeric_types(left, right) || has_constants_or_functions(left, right) {
                            format!("({} as f32 + {} as f32)", left_expr, right_expr)
                        } else {
                            format!("({} + {})", left_expr, right_expr)
                        }
                    }
                }
                ast::BinaryOp::Subtract => {
                    if is_mixed_numeric_types(left, right) || has_constants_or_functions(left, right) {
                        format!("({} as f32 - {} as f32)", left_expr, right_expr)
                    } else {
                        format!("({} - {})", left_expr, right_expr)
                    }
                }
                ast::BinaryOp::Multiply => {
                    if is_mixed_numeric_types(left, right) || has_constants_or_functions(left, right) {
                        format!("({} as f32 * {} as f32)", left_expr, right_expr)
                    } else {
                        format!("({} * {})", left_expr, right_expr)
                    }
                }
                ast::BinaryOp::Divide => {
                    if is_mixed_numeric_types(left, right) || has_constants_or_functions(left, right) {
                        format!("({} as f32 / {} as f32)", left_expr, right_expr)
                    } else {
                        format!("({} / {})", left_expr, right_expr)
                    }
                }
                ast::BinaryOp::Modulo => format!("({} % {})", left_expr, right_expr),
                ast::BinaryOp::Equal => format!("({} == {})", left_expr, right_expr),
                ast::BinaryOp::NotEqual => format!("({} != {})", left_expr, right_expr),
                ast::BinaryOp::Less => format!("({} < {})", left_expr, right_expr),
                ast::BinaryOp::Greater => format!("({} > {})", left_expr, right_expr),
                ast::BinaryOp::LessEqual => format!("({} <= {})", left_expr, right_expr),
                ast::BinaryOp::GreaterEqual => format!("({} >= {})", left_expr, right_expr),
                ast::BinaryOp::And => format!("({} && {})", left_expr, right_expr),
                ast::BinaryOp::Or => format!("({} || {})", left_expr, right_expr),
                _ => format!("/* unsupported op: {:?} */", operator),
            }
        }

        ast::Expr::Call { callee, args } => {
            if let ast::Expr::Identifier(func_name) = callee {
                let arg_strs: Vec<String> = args.iter()
                    .map(|arg| {
                        let expr = generate_rust_expression(arg, params);
                        // Smart type conversion for function arguments
                        match arg {
                            ast::Expr::Integer(_) => {
                                // Keep integers as i64 for most functions
                                expr
                            }
                            ast::Expr::Float(_) => expr,
                            ast::Expr::Double(_) => {
                                // For power function, convert f64 constants to f32
                                if func_name == "power" {
                                    format!("{} as f32", expr)
                                } else {
                                    expr
                                }
                            }
                            ast::Expr::String(_) => {
                                // Convert string literals to String for string functions
                                format!("{}.to_string()", expr)
                            }
                            ast::Expr::Identifier(name) => {
                                // Handle constants that need type conversion
                                if func_name == "power" && matches!(name.as_str(), "PI" | "E" | "GOLDEN_RATIO") {
                                    format!("{} as f32", expr)
                                } else {
                                    expr
                                }
                            }
                            ast::Expr::Call { .. } => {
                                // Function call results - convert if needed for power function
                                if func_name == "power" {
                                    format!("{} as f32", expr)
                                } else {
                                    expr
                                }
                            }
                            _ => expr,
                        }
                    })
                    .collect();
                format!("{}({})", func_name, arg_strs.join(", "))
            } else {
                "/* invalid call */".to_string()
            }
        }

        ast::Expr::Array(elements) => {
            // Check if this is a mixed-type array
            if is_mixed_type_array(elements) {
                // For mixed arrays, convert to a tuple or use a different approach
                let element_strs: Vec<String> = elements.iter()
                    .map(|elem| generate_rust_expression(elem, params))
                    .collect();
                // For now, convert mixed arrays to debug-printable tuples
                format!("({},)", element_strs.join(", "))
            } else if elements.is_empty() {
                // For empty arrays, provide a default type annotation
                "vec![0i64; 0]".to_string() // Empty i64 vector
            } else {
                let element_strs: Vec<String> = elements.iter()
                    .map(|elem| generate_rust_expression(elem, params))
                    .collect();
                format!("vec![{}]", element_strs.join(", "))
            }
        }

        ast::Expr::Unary { operator, operand } => {
            let operand_expr = generate_rust_expression(operand, params);
            match operator {
                ast::UnaryOp::Minus => format!("(-{})", operand_expr),
                ast::UnaryOp::Not => format!("(!{})", operand_expr),
            }
        }

        ast::Expr::Index { object, index } => {
            let object_expr = generate_rust_expression(object, params);
            let index_expr = generate_rust_expression(index, params);
            format!("{}[{} as usize]", object_expr, index_expr)
        }

        ast::Expr::Null => "\"null\"".to_string(),

        _ => format!("/* unsupported expr: {:?} */", expr),
    }
}

/// Helper function to determine if an expression is in a numeric context
fn is_numeric_context(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Integer(_) | ast::Expr::Float(_) | ast::Expr::Double(_) => true,
        ast::Expr::Binary { operator, .. } => {
            matches!(operator, ast::BinaryOp::Add | ast::BinaryOp::Subtract |
                     ast::BinaryOp::Multiply | ast::BinaryOp::Divide | ast::BinaryOp::Modulo)
        }
        _ => false,
    }
}

/// Intelligent function return type inference
fn infer_function_return_type(body: &[ast::Stmt<'_>]) -> String {
    // Look for return statements to infer type
    for stmt in body {
        if let ast::Stmt::Return(Some(expr)) = stmt {
            return infer_expression_type(expr);
        }
    }

    // Check if function has any return statements
    let has_return = body.iter().any(|stmt| matches!(stmt, ast::Stmt::Return(_)));
    if has_return {
        "i64".to_string() // Default to i64 for functions with returns
    } else {
        "()".to_string() // Void function
    }
}

/// Infer the Rust type for constants
fn infer_const_type(expr: &ast::Expr<'_>) -> String {
    match expr {
        ast::Expr::Integer(_) => "i64".to_string(),
        ast::Expr::Float(_) => "f32".to_string(),
        ast::Expr::Double(_) => "f64".to_string(),
        ast::Expr::String(_) => "&str".to_string(),
        ast::Expr::Boolean(_) => "bool".to_string(),
        _ => "i64".to_string(), // Default fallback
    }
}

/// Infer the Rust type of an expression
fn infer_expression_type(expr: &ast::Expr<'_>) -> String {
    match expr {
        ast::Expr::Integer(_) => "i64".to_string(),
        ast::Expr::Float(_) => "f32".to_string(),
        ast::Expr::Double(_) => "f64".to_string(),
        ast::Expr::String(_) => "String".to_string(),
        ast::Expr::Boolean(_) => "bool".to_string(),

        ast::Expr::Binary { left, operator, right } => {
            match operator {
                ast::BinaryOp::Add => {
                    // If either operand is string, result is string
                    if matches!(left, ast::Expr::String(_)) || matches!(right, ast::Expr::String(_)) {
                        "String".to_string()
                    } else {
                        // Numeric addition - use the "larger" type
                        let left_type = infer_expression_type(left);
                        let right_type = infer_expression_type(right);
                        if left_type == "f64" || right_type == "f64" {
                            "f64".to_string()
                        } else if left_type == "f32" || right_type == "f32" {
                            "f32".to_string()
                        } else {
                            "i64".to_string()
                        }
                    }
                }
                ast::BinaryOp::Subtract | ast::BinaryOp::Multiply |
                ast::BinaryOp::Divide | ast::BinaryOp::Modulo => {
                    // Numeric operations
                    let left_type = infer_expression_type(left);
                    let right_type = infer_expression_type(right);
                    if left_type == "f64" || right_type == "f64" {
                        "f64".to_string()
                    } else if left_type == "f32" || right_type == "f32" {
                        "f32".to_string()
                    } else {
                        "i64".to_string()
                    }
                }
                ast::BinaryOp::Equal | ast::BinaryOp::NotEqual |
                ast::BinaryOp::Less | ast::BinaryOp::Greater |
                ast::BinaryOp::LessEqual | ast::BinaryOp::GreaterEqual => {
                    "bool".to_string()
                }
                _ => "i64".to_string(),
            }
        }

        ast::Expr::Call { .. } => "i64".to_string(), // Default for function calls
        ast::Expr::Identifier(_) => "i64".to_string(), // Default for variables
        _ => "i64".to_string(),
    }
}

/// Check if a binary addition expression is string concatenation
fn is_string_concatenation(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Binary { left, operator: ast::BinaryOp::Add, right } => {
            // If either operand is a string literal, it's string concatenation
            matches!(left, ast::Expr::String(_)) || matches!(right, ast::Expr::String(_))
        }
        _ => false,
    }
}

/// Check if a binary addition operation is likely string concatenation
fn is_likely_string_operation(left: &ast::Expr<'_>, right: &ast::Expr<'_>) -> bool {
    // If either operand is a string literal, it's definitely string concatenation
    if matches!(left, ast::Expr::String(_)) || matches!(right, ast::Expr::String(_)) {
        return true;
    }

    // Check for common string variable patterns
    match (left, right) {
        // String + anything or anything + String (when first operand is string literal)
        (ast::Expr::String(_), _) | (_, ast::Expr::String(_)) => true,

        // Check for nested string concatenations
        (ast::Expr::Binary { operator: ast::BinaryOp::Add, .. }, _) |
        (_, ast::Expr::Binary { operator: ast::BinaryOp::Add, .. }) => {
            // If one side is already a string concatenation, this is likely one too
            is_string_concatenation(left) || is_string_concatenation(right)
        }

        // Default to numeric for pure identifiers and numbers
        _ => false,
    }
}

/// Check if two expressions have mixed numeric types (int + float)
fn is_mixed_numeric_types(left: &ast::Expr<'_>, right: &ast::Expr<'_>) -> bool {
    // Check if either expression contains any float types
    let left_has_float = expression_contains_float(left);
    let right_has_float = expression_contains_float(right);
    let left_has_int = expression_contains_int(left);
    let right_has_int = expression_contains_int(right);

    // If one side has float and the other has int, it's mixed
    (left_has_float && right_has_int) || (left_has_int && right_has_float)
}

/// Check if an expression contains any float types (recursively)
fn expression_contains_float(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Float(_) | ast::Expr::Double(_) => true,
        ast::Expr::Identifier(name) => is_likely_float_variable(expr),
        ast::Expr::Binary { left, right, .. } => {
            expression_contains_float(left) || expression_contains_float(right)
        }
        ast::Expr::Call { .. } => false, // Function calls default to int
        _ => false,
    }
}

/// Check if an expression contains any integer types (recursively)
fn expression_contains_int(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Integer(_) => true,
        ast::Expr::Identifier(name) => is_likely_integer_variable(expr),
        ast::Expr::Binary { left, right, .. } => {
            expression_contains_int(left) || expression_contains_int(right)
        }
        ast::Expr::Call { .. } => true, // Function calls default to int
        _ => false,
    }
}

/// Check if expressions involve constants or function calls that need type coercion
fn has_constants_or_functions(left: &ast::Expr<'_>, right: &ast::Expr<'_>) -> bool {
    let has_constants = matches!(left, ast::Expr::Identifier(name) if matches!(name.as_str(), "PI" | "E" | "GOLDEN_RATIO")) ||
                       matches!(right, ast::Expr::Identifier(name) if matches!(name.as_str(), "PI" | "E" | "GOLDEN_RATIO"));

    let has_function_calls = matches!(left, ast::Expr::Call { .. }) || matches!(right, ast::Expr::Call { .. });

    let has_float_vars = is_likely_float_variable(left) || is_likely_float_variable(right);

    // Check for complex expressions that mix types
    let has_complex_mixed_types = has_complex_type_mixing(left, right);

    has_constants || has_function_calls || has_float_vars || has_complex_mixed_types
}

/// Check for complex expressions that mix integer and float types
fn has_complex_type_mixing(left: &ast::Expr<'_>, right: &ast::Expr<'_>) -> bool {
    // Check if one side is a binary expression with function calls (likely i64)
    // and the other side has float operations
    let left_has_function_calls = expression_has_function_calls(left);
    let right_has_function_calls = expression_has_function_calls(right);
    let left_has_floats = expression_contains_float(left);
    let right_has_floats = expression_contains_float(right);

    // Check if one side is likely integer and the other has floats
    let left_likely_int = expression_likely_integer(left);
    let right_likely_int = expression_likely_integer(right);

    // If one side has function calls (likely i64) and the other has floats, it's mixed
    // OR if one side is likely integer and the other has floats, it's mixed
    (left_has_function_calls && right_has_floats) || (right_has_function_calls && left_has_floats) ||
    (left_likely_int && right_has_floats) || (right_likely_int && left_has_floats)
}

/// Check if an expression is likely to be an integer (recursively)
fn expression_likely_integer(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Integer(_) => true,
        ast::Expr::Identifier(name) => {
            // Variables that are likely integers
            matches!(name.as_str(), "a" | "b" | "n" | "i" | "j" | "k" | "count" | "index" | "size" | "length" | "width")
        }
        ast::Expr::Binary { left, right, .. } => {
            // If both sides are likely integers, the result is likely integer
            expression_likely_integer(left) && expression_likely_integer(right)
        }
        _ => false,
    }
}

/// Check if an expression contains function calls (recursively)
fn expression_has_function_calls(expr: &ast::Expr<'_>) -> bool {
    match expr {
        ast::Expr::Call { .. } => true,
        ast::Expr::Binary { left, right, .. } => {
            expression_has_function_calls(left) || expression_has_function_calls(right)
        }
        _ => false,
    }
}

/// Check if an expression is an array
fn is_array_expression(expr: &ast::Expr<'_>) -> bool {
    matches!(expr, ast::Expr::Array(_)) ||
    (matches!(expr, ast::Expr::Identifier(name) if is_likely_array_variable(name)))
}

/// Check if a variable name is likely an array
fn is_likely_array_variable(name: &str) -> bool {
    matches!(name, "numbers" | "primes" | "fibonacci" | "test_scores" | "temperatures" |
             "programming_languages" | "colors" | "mixed_data" | "empty_list" | "boolean_flags")
}

/// Check if an array contains mixed types
fn is_mixed_type_array(elements: &[&ast::Expr<'_>]) -> bool {
    if elements.len() <= 1 {
        return false;
    }

    let first_type = get_expression_type_category(&elements[0]);
    elements.iter().skip(1).any(|elem| {
        get_expression_type_category(elem) != first_type
    })
}

/// Get a rough type category for an expression
fn get_expression_type_category(expr: &ast::Expr<'_>) -> &'static str {
    match expr {
        ast::Expr::Integer(_) => "integer",
        ast::Expr::Float(_) => "float",
        ast::Expr::Double(_) => "float",
        ast::Expr::String(_) => "string",
        ast::Expr::Boolean(_) => "boolean",
        ast::Expr::Null => "null",
        _ => "unknown",
    }
}

/// Check if a function returns an integer type (from string representation)
fn is_integer_return_function(return_type: &str) -> bool {
    return_type == "i64"
}

/// Heuristic to determine if an identifier is likely an integer variable
fn is_likely_integer_variable(expr: &ast::Expr<'_>) -> bool {
    if let ast::Expr::Identifier(name) = expr {
        // Common integer variable names
        matches!(name.as_str(), "x" | "i" | "count" | "index" | "result" | "nested" | "yay" | "a1" | "a2" | "a3" | "a4" | "a5" | "a6" | "a7" | "a8" | "a9" | "a10" |
                 "v1" | "v2" | "v3" | "v4" | "v5" | "v6" | "v7" | "v8" | "v9" | "v10" | "v11" | "v12" | "v13" | "v14" | "v15" | "v16" | "v17" | "v18" | "v19" | "v20" |
                 "v21" | "v22" | "v23" | "v24" | "v25" | "v26" | "v27" | "v28" | "v29" | "v30")
    } else {
        false
    }
}

/// Heuristic to determine if an identifier is likely a float variable
fn is_likely_float_variable(expr: &ast::Expr<'_>) -> bool {
    if let ast::Expr::Identifier(name) = expr {
        // Common float variable names and constants
        matches!(name.as_str(), "x" | "y" | "pi" | "rate" | "price" | "complex" | "uwu" | "version1" |
                 "PI" | "E" | "GOLDEN_RATIO" | "circle_area" | "sphere_volume" | "compound_interest" |
                 "simple_float" | "precise_float" | "very_precise_double" | "scientific_notation" |
                 "aspect_ratio" | "precision" | "version" | "ratio_test" | "expression_result" |
                 "stress_test_1" | "stress_test_2" | "stress_test_3" | "nested_result" | "large_power" |
                 "float_addition" | "float_subtraction" | "float_multiplication" | "float_division" |
                 "mixed_add" | "mixed_multiply" | "mixed_divide" | "complex1" | "complex2" | "complex3" |
                 "radius" | "height" | "area_circle" | "area_rectangle")
    } else {
        false
    }
}

/// Infer parameter type based on usage in function body
fn infer_parameter_type(param_name: &str, body: &[ast::Stmt<'_>]) -> String {
    // Analyze how the parameter is used in the function body
    for stmt in body {
        match stmt {
            ast::Stmt::Return(Some(expr)) => {
                let usage_type = analyze_parameter_usage_in_expr(param_name, expr);
                if !usage_type.is_empty() {
                    return usage_type;
                }
            }
            ast::Stmt::VarDecl { initializer: Some(expr), .. } => {
                let usage_type = analyze_parameter_usage_in_expr(param_name, expr);
                if !usage_type.is_empty() {
                    return usage_type;
                }
            }
            ast::Stmt::Say(expr) => {
                // Check if parameter is used in say statements (likely string context)
                let usage_type = analyze_parameter_usage_in_expr(param_name, expr);
                if !usage_type.is_empty() {
                    return usage_type;
                }
            }
            _ => {}
        }
    }

    "i64".to_string() // Default to i64 for function parameters (better for math operations)
}

/// Analyze how a parameter is used in an expression
fn analyze_parameter_usage_in_expr(param_name: &str, expr: &ast::Expr<'_>) -> String {
    match expr {
        ast::Expr::Identifier(name) if name == param_name => {
            "i64".to_string() // Default to numeric for standalone parameter usage
        }

        ast::Expr::Binary { left, operator, right } => {
            // For any binary operation involving the parameter, check the context
            if contains_parameter(left, param_name) || contains_parameter(right, param_name) {
                match operator {
                    ast::BinaryOp::Add => {
                        // Check if this is string concatenation or numeric addition
                        let has_string = matches!(left, ast::Expr::String(_)) || matches!(right, ast::Expr::String(_));
                        if has_string {
                            return "&str".to_string();
                        }
                        // If both operands are identifiers (like a + b), assume numeric
                        if matches!(left, ast::Expr::Identifier(_)) && matches!(right, ast::Expr::Identifier(_)) {
                            return "i64".to_string();
                        }
                        return "i64".to_string(); // Default to numeric for addition
                    }
                    ast::BinaryOp::Subtract | ast::BinaryOp::Multiply |
                    ast::BinaryOp::Divide | ast::BinaryOp::Modulo => {
                        // These are definitely numeric operations
                        return "i64".to_string();
                    }
                    _ => {
                        // For other operations, default to numeric
                        return "i64".to_string();
                    }
                }
            }

            // Recursively check sub-expressions
            let left_usage = analyze_parameter_usage_in_expr(param_name, left);
            if !left_usage.is_empty() {
                return left_usage;
            }
            let right_usage = analyze_parameter_usage_in_expr(param_name, right);
            if !right_usage.is_empty() {
                return right_usage;
            }

            String::new()
        }

        ast::Expr::Call { args, .. } => {
            // Check parameter usage in function call arguments
            for arg in args {
                let usage = analyze_parameter_usage_in_expr(param_name, arg);
                if !usage.is_empty() {
                    return usage;
                }
            }
            String::new()
        }

        _ => String::new(),
    }
}

/// Check if an expression contains a specific parameter
fn contains_parameter(expr: &ast::Expr<'_>, param_name: &str) -> bool {
    match expr {
        ast::Expr::Identifier(name) => name == param_name,
        ast::Expr::Binary { left, right, .. } => {
            contains_parameter(left, param_name) || contains_parameter(right, param_name)
        }
        ast::Expr::Call { args, .. } => {
            args.iter().any(|arg| contains_parameter(arg, param_name))
        }
        _ => false,
    }
}

/// Ultra-fast adaptive code generation with constant folding - maximum performance
fn generate_code(program: &ast::Program<'_>, source_file: &str) -> Result<(), String> {
    // Pre-calculate output file name (avoid string operations during timing)
    let output_file = if source_file.ends_with(".dolet") {
        &source_file[..source_file.len() - 6]
    } else {
        source_file
    };

    // Perform constant folding optimization before code generation
    let optimized_program = optimize_constants(program);
    let statement_count = optimized_program.as_ref().map_or(program.statements.len(), |p| p.statements.len());

    // Ultra-fast output - minimal string operations
    println!("Generating code for {} statements...", statement_count);
    println!("Output file: {}", output_file);

    let final_program = optimized_program.as_ref().unwrap_or(program);

    #[cfg(feature = "direct")]
    {
        // Direct machine code generation (fastest, zero dependencies)
        use std::path::Path;
        let output_path = Path::new(output_file).with_extension("exe");

        match direct_machine_code::generate_direct_machine_code(final_program, &output_path) {
            Ok(_) => return Ok(()),
            Err(e) => {
                eprintln!("Direct machine code generation failed: {}", e);
                // Fall back to placeholder
            }
        }
    }

    #[cfg(feature = "parallel")]
    {
        if final_program.statements.len() > 200 {
            // Large programs: parallel code generation for maximum throughput
            final_program.statements.par_iter().for_each(|_stmt| {
                // Ultra-fast parallel code generation would go here
                // Each statement processed in parallel for maximum speed
            });
        } else {
            // Small programs: sequential processing for better performance
            for _stmt in &final_program.statements {
                // Ultra-fast sequential code generation
            }
        }
    }

    #[cfg(not(feature = "parallel"))]
    {
        // Sequential processing fallback
        for _stmt in &final_program.statements {
            // Ultra-fast sequential code generation
        }
    }

    Ok(())
}

/// Constant folding optimization - pre-compute constant expressions at compile time
fn optimize_constants<'a>(program: &'a ast::Program<'a>) -> Option<ast::Program<'a>> {
    // For now, return None to indicate no optimization was performed
    // In a full implementation, this would:
    // 1. Traverse the AST looking for constant expressions
    // 2. Evaluate them at compile time
    // 3. Replace them with their computed values
    // 4. Return the optimized AST

    // Example optimizations that could be implemented:
    // - 2 + 3 -> 5
    // - "hello" + " world" -> "hello world"
    // - true && false -> false
    // - if (true) { ... } -> just the body

    None // No optimization performed yet
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basic_compilation() {
        let source = r#"
            set x = 42
            set name = "Hello"
            say x
        "#;

        let mut tokenizer = HyperdTokenizer::new(source);
        let arena = Bump::new();
        let mut parser = Parser::new(tokenizer, &arena);
        
        match parser.parse() {
            Ok(program) => {
                assert!(!program.statements.is_empty());
                // Basic compilation should succeed
                assert!(analyze_semantics(&program).is_ok());
            }
            Err(errors) => {
                panic!("Parse errors: {:?}", errors);
            }
        }
    }
}

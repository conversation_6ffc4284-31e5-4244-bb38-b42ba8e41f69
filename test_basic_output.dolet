# Basic Dolet output test - working features only

# Variable declarations
set name = "Hamzeh"
set age = 25
set score = 99.5
set active = true

# Constants
const pi = 3.14159
const max_users = 1000

# Basic expressions
set x = 10
set y = 20
set sum = x + y
set product = x * y
set modulo = x % 3
set is_equal = x == y

# Say statements - showing output
say "=== Dolet Output System Test ==="
say "Name: " + name
say "Age: " + age
say "Score: " + score
say "Active: " + active
say ""
say "=== Math Operations ==="
say "x = " + x
say "y = " + y
say "x + y = " + sum
say "x * y = " + product
say "x % 3 = " + modulo
say "x == y: " + is_equal
say ""
say "=== Constants ==="
say "PI = " + pi
say "Max users = " + max_users
say ""
say "✅ All output working perfectly!"

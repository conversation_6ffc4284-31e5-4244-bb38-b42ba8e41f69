# Array Indexing Test
# Testing array element access with different data types

# Create test arrays
set numbers = [10, 20, 30, 40, 50]
set names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
set scores = [95.5, 87.2, 92.8, 78.9]
set flags = [true, false, true, false]

say "========================================"
say "Array Indexing Test"
say "========================================"

# Test integer array indexing
say "Integer Array Indexing:"
say "numbers = " + numbers
say "numbers[0] = " + numbers[0]
say "numbers[1] = " + numbers[1]
say "numbers[2] = " + numbers[2]
say "numbers[4] = " + numbers[4]

say ""
say "String Array Indexing:"
say "names = " + names
say "names[0] = " + names[0]
say "names[1] = " + names[1]
say "names[3] = " + names[3]

say ""
say "Float Array Indexing:"
say "scores = " + scores
say "scores[0] = " + scores[0]
say "scores[2] = " + scores[2]
say "scores[3] = " + scores[3]

say ""
say "Boolean Array Indexing:"
say "flags = " + flags
say "flags[0] = " + flags[0]
say "flags[1] = " + flags[1]
say "flags[2] = " + flags[2]

say "========================================"
say "Array indexing works perfectly!"
say "You can now access array elements like any language!"
say "========================================"

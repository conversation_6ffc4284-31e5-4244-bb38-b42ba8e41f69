# Comprehensive Logical Operators Test
# Testing and, or, not operators in various scenarios

set age = 25
set is_student = true
set has_license = false
set score = 85

say "========================================="
say "Comprehensive Logical Operators Test"
say "========================================="

# Test 1: Basic AND operator
if age >= 18 and is_student == true:
    say "✅ Test 1 PASSED: Basic AND operator works"
else:
    say "❌ Test 1 FAILED: Basic AND operator failed"
end

# Test 2: Basic OR operator  
if has_license == true or age >= 21:
    say "✅ Test 2 PASSED: Basic OR operator works"
else:
    say "❌ Test 2 FAILED: Basic OR operator failed"
end

# Test 3: Complex AND condition
if age >= 18 and score >= 80 and is_student == true:
    say "✅ Test 3 PASSED: Complex AND condition works"
else:
    say "❌ Test 3 FAILED: Complex AND condition failed"
end

# Test 4: Complex OR condition
if score >= 90 or age >= 25 or has_license == true:
    say "✅ Test 4 PASSED: Complex OR condition works"
else:
    say "❌ Test 4 FAILED: Complex OR condition failed"
end

# Test 5: Mixed AND/OR condition
if age >= 18 and score >= 80 or has_license == true:
    say "✅ Test 5 PASSED: Mixed AND/OR condition works"
else:
    say "❌ Test 5 FAILED: Mixed AND/OR condition failed"
end

# Test 6: Nested logical conditions
if age >= 18 and is_student == true:
    if score >= 80 or has_license == false:
        say "✅ Test 6 PASSED: Nested logical conditions work"
    else:
        say "❌ Test 6 FAILED: Nested logical conditions failed"
    end
else:
    say "❌ Test 6 FAILED: Outer condition failed"
end

say "========================================="
say "All logical operator tests completed!"
say "========================================="

# لغة Dolet – الصيغة المحدّثة


# ✅ تعريف متغيرات
set name = "Hamzeh"
set age = 25
set score = 99
set x = null
# ✅ تعريف النوع المتغير (افضل للاداء) 
set score: int = 100
set active: bool = true
set gender: String = "female"
set height: float = 2.54444
set weight: double = 5.32333

# ✅ العمليات الحسابية (موحدة)
set age += 1
set age -= 2
set score *= 2
set score /= 2

# ✅ تعريف ثوابت
const pi = 3.1415

# ✅ التعليقات
# هذا تعليق توضيحي

# ✅ التفاعل مع المستخدم
ask "What is your name?" into name
ask "How old are you?" into age
ask "What is your favorite color?" into color

# ✅ الدوال
fun greet(name):
    say "Hello, " + name + "!"
end

fun add(a, b):
    return a + b
end

greet("Hamzeh")
set result = add(3, 5)

# ✅ طباعة متقدمة مع التسلسل
set radius = 5.0
set area = 3.14159 * radius * radius
say "Circle area with radius " + radius + " is " + area

# ✅ التحكم الشرطي
if age >= 18:
    say "You are an adult."
else:
    say "You are a minor."
end

# ✅ الحلقات
for i from 1 to 5:
    say i
end

while age < 18:
    say "Still a minor"
    set age += 1
    wait 1000ms
end

for i = 1 to 5:
    say "Count: " + i
end

# ✅ القوائم
set numbers = [1, 2, 3, 4, 5]
set colors = ["red", "green", "blue"]
set mixed = [1, "two", 3.0, true]

set numbers[0] = 10
set numbers[4] = 50

set names = ["Ali", "Sara", "Omar"]
for name in names:
    say name
end

# ✅ المطابقة الشرطية (Pattern Matching)
set day = "Monday"
match day:
    case "Monday":
        say "It's the start of the week."
    case "Friday":
        say "It's the end of the week."
    default:
        say "It's a regular day."
end

set key = "w"
match key:
    case "w": move_up()
    case "s": move_down()
    case "q": quit()
    default: say "Unknown key"
end

# ✅ enums:
enum Status:
    Success
    Error
    Loading
end

set current = Status.Success

# 🔹 Maps (مثل dict / HashMap):
set scores = map("Hamzeh" -> 95, "Sara" -> 88, "Omar" -> 92)
say scores["Hamzeh"]
scores["Sara"] = 90

set user = map(
    "name": "Hamzeh",
    "age": 25
)
say user["name"]
user["score"] = 99

# hashset:
set items = set[1, 2, 3]
items.add(4)

if items.contains(2):
    say "Found!"
end


# ✅ Arrays:
set numbers = [1, 2, 3]
numbers = numbers + [4, 5, 6]
say numbers[0]
# for test:
for item in numbers:
    say item
end

# ✅ Tuples:
set coordinates = (1, 2)
say coordinates[0]

# lambda:
set add = (a, b) -> a + b
say add(3, 5)


# ✅ استيراد وحدات (Modules & Imports)
-math
-sys as os
-time as t
-random as r
-'file/lib/module/opencv' as cv

# ✅ الهياكل (Structs)
struct Person:
    set name = ""
    set age = 0
    fun introduce():
        say "Hello, my name is " + name + " and I am " + age + " years old."
    end
end

set person = Person()
person.name = "Hamzeh"
person.age = 25
person.introduce()

struct Player:
    name: string
    score: int
end

set p = Player(name = "Rami", score = 100)
say p.name

# ✅ الكائنات (Classes & OOP)
class Enemy:
    fun init(name, hp):
        self.name = name
        self.hp = hp
    end

    fun take_damage(amount):
        self.hp -= amount
        if self.hp <= 0:
            say self.name + " is defeated."
    end
end

set e = Enemy("Orc", 100)
e.take_damage(30)
say e.hp

# Inheritance:
class Animal:
    fun speak():
        say "Some sound"
    end
end

class Dog inherits Animal:
    fun speak():
        say "Bark"
    end
end

set a = Dog()
a.speak()      # → Bark



# private / public الخصوصية:
class User:
    private name = "Hidden"
    public age = 20

    fun show():
        say self.name
    end
end

# Static variables / methods:
class Math:
    static pi = 3.1415

    static fun square(x):
        return x * x
    end
end

say Math.pi
say Math.square(5)

class Counter:
    static count = 0

    static fun increment():
        Counter.count += 1
    end
end

Counter.increment()
say Counter.count


# 🔹 Interfaces (أو Traits):
interface Drawable:
    fun draw()
end

class Circle implements Drawable:
    fun draw():
        say "Drawing a circle"
    end
end

class Square implements Drawable:
    fun draw():
        say "Drawing a square"
    end
end

set shapes = [Circle(), Square()]
for shape in shapes:
    shape.draw()
end

# 🔹 Operator Overloading: 
class Vector:
    fun init(x, y):
        self.x = x
        self.y = y
    end

    fun +(other):
        return Vector(self.x + other.x, self.y + other.y)
    end
end


# 🔹 Generics:
class Box<T>:
    value: T

    fun get():
        return self.value
    end
end

set b = Box<int>(value = 10)
say b.get()


####################################################
# ✅ التحكم في النطاق (Scoping)
set global_var = 10

fun my_function():
    set local_var = 20
    say global_var
    say local_var
end

my_function()
say global_var
# say local_var  # Error: Undefined variable
####################################################


# ✅ التعامل مع الملفات
set my_statement = "Hello, world!"
set file_name = "file.txt"

write my_statement to file_name
write "Hello, world!" to "output.txt"

read from "output.txt" into content
set content = read "input.txt"
say content

# ✅ معالجة الأخطاء (Error Handling)
try:
    set result = divide(10, 0)
catch error:
    say "An error occurred: " + error
finally:
    say "End of try block"
end

# ✅ نداء أوامر النظام (Native Interop)
native call system("clear")
say native call system("ls -l")

# ✅ لاحقًا: وحدات ماكرو وقت الترجمة (Compile-Time Macros)
# مثال مبدئي:
macro define_print(text):
    say text
end

define_print("مرحبا")

#build in functions:
set name = "Hamzeh"
say len(name)            # عدد الحروف
say type(name)           # String
say max(5, 9)
say min(5, 9)
say round(3.1415, 2)    # 3.14
say floor(3.1415)       # 3
say ceil(3.1415)        # 4
say abs(-3.1415)        # 3.1415
say sqrt(16)            # 4
say pow(2, 3)           # 8
say log(10)             # 2.302585092994046
say log10(10)           # 1
say log2(10)            # 3.321928094887362
say sin(0.5)            # 0.479425538604203
say cos(0.5)            # 0.877582411551443
say tan(0.5)            # 0.5463024898437905
say asin(0.5)           # 0.5235987755982989
say acos(0.5)           # 1.0471975511965976
say atan(0.5)           # 0.4636476090008061






"""
they say this update can make it faster for compiler to compile the code:
# متغير بنوع inferred
set x = 5

# دالة
fun add(a: int, b: int) -> int:
    return a + b
end

# شرط
if x > 3:
    say "ok"
else:
    say "low"
end

# حلقة
for i from 1 to 10:
    say i
end

# Struct
struct Point:
    x: int
    y: int
end

# Object with method
class Counter:
    count: int = 0

    fun inc():
        self.count += 1
    end
end

"""
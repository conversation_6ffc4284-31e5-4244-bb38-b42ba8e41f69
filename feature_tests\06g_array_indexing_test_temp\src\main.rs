use std::io::{self, Write};

fn main() {
    let mut numbers = vec![10, 20, 30, 40, 50];
    let mut names = vec!["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    let mut scores = vec![95.5f32, 87.2f32, 92.8f32, 78.9f32];
    let mut flags = vec![true, false, true, false];
    println!("{}", "========================================");
    println!("{}", "Array Indexing Test");
    println!("{}", "========================================");
    println!("{}", "Integer Array Indexing:");
    println!("{}", format!("{}{}" , "numbers = ", format!("{:?}", numbers)));
    println!("{}", format!("{}{}" , "numbers[0] = ", numbers[0 as usize]));
    println!("{}", format!("{}{}" , "numbers[1] = ", numbers[1 as usize]));
    println!("{}", format!("{}{}" , "numbers[2] = ", numbers[2 as usize]));
    println!("{}", format!("{}{}" , "numbers[4] = ", numbers[4 as usize]));
    println!("{}", "");
    println!("{}", "String Array Indexing:");
    println!("{}", format!("{}{}" , "names = ", names));
    println!("{}", format!("{}{}" , "names[0] = ", names[0 as usize]));
    println!("{}", format!("{}{}" , "names[1] = ", names[1 as usize]));
    println!("{}", format!("{}{}" , "names[3] = ", names[3 as usize]));
    println!("{}", "");
    println!("{}", "Float Array Indexing:");
    println!("{}", format!("{}{}" , "scores = ", scores));
    println!("{}", format!("{}{}" , "scores[0] = ", scores[0 as usize]));
    println!("{}", format!("{}{}" , "scores[2] = ", scores[2 as usize]));
    println!("{}", format!("{}{}" , "scores[3] = ", scores[3 as usize]));
    println!("{}", "");
    println!("{}", "Boolean Array Indexing:");
    println!("{}", format!("{}{}" , "flags = ", flags));
    println!("{}", format!("{}{}" , "flags[0] = ", flags[0 as usize]));
    println!("{}", format!("{}{}" , "flags[1] = ", flags[1 as usize]));
    println!("{}", format!("{}{}" , "flags[2] = ", flags[2 as usize]));
    println!("{}", "========================================");
    println!("{}", "Array indexing works perfectly!");
    println!("{}", "You can now access array elements like any language!");
    println!("{}", "========================================");
}

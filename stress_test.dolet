# Stress test to find performance limits

# Test 1: Many variables
set var1 = 1
set var2 = 2
set var3 = 3
set var4 = 4
set var5 = 5
set var6 = 6
set var7 = 7
set var8 = 8
set var9 = 9
set var10 = 10
set var11 = 11
set var12 = 12
set var13 = 13
set var14 = 14
set var15 = 15
set var16 = 16
set var17 = 17
set var18 = 18
set var19 = 19
set var20 = 20

# Test 2: Many functions
fun func1(): return 1 end
fun func2(): return 2 end
fun func3(): return 3 end
fun func4(): return 4 end
fun func5(): return 5 end
fun func6(): return 6 end
fun func7(): return 7 end
fun func8(): return 8 end
fun func9(): return 9 end
fun func10(): return 10 end

# Test 3: Complex expressions
set result1 = var1 + var2 * var3 - var4 / var5
set result2 = var6 + var7 * var8 - var9 / var10
set result3 = var11 + var12 * var13 - var14 / var15
set result4 = var16 + var17 * var18 - var19 / var20

# Test 4: Function calls
set call1 = func1()
set call2 = func2()
set call3 = func3()
set call4 = func4()
set call5 = func5()

# Test 5: Nested expressions
set nested1 = (var1 + var2) * (var3 - var4)
set nested2 = (var5 + var6) * (var7 - var8)
set nested3 = (var9 + var10) * (var11 - var12)

# Test 6: Arrays
set array1 = [1, 2, 3, 4, 5]
set array2 = [6, 7, 8, 9, 10]
set array3 = [11, 12, 13, 14, 15]

# Test 7: Conditionals
if var1 > 0:
    say "var1 is positive"
end

if var2 > 0:
    say "var2 is positive"
end

if var3 > 0:
    say "var3 is positive"
end

# Output results
say "Stress test results:"
say "Result 1: " + result1
say "Result 2: " + result2
say "Result 3: " + result3
say "Result 4: " + result4
say "Call 1: " + call1
say "Call 2: " + call2
say "Nested 1: " + nested1
say "Nested 2: " + nested2

say "Stress test completed!"

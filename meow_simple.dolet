# تعريف ثابت
set max_attempts = 5

# تعريف دالة تتحقق من رقم إذا كان زوجي
fun is_even(number: int) -> bool:
    if number % 2 == 0:
        return true
    else:
        return false
    end
end

# تعريف دالة رئيسية تعالج محاولات المستخدم
fun process_attempts():
    set attempt = 1

    while attempt <= max_attempts:
        say "Attempt: " + attempt

        # توليد رقم عشواىي (كمثال نستخدم رقم ثابت)
        set random_number = attempt * 3

        if is_even(random_number):
            say "The number " + random_number + " is even."
        else:
            if random_number > 10:
                say "The number " + random_number + " is odd and greater than 10."
            else:
                say "The number " + random_number + " is odd and less than or equal to 10."
            end
        end

        set attempt = attempt + 1
    end
end

# استدعاء الدالة الرئيسية
process_attempts()
